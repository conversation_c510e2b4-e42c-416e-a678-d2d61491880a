<template>
  <div class="responsive-card-container">
    <!-- 桌面端表格视图 -->
    <div v-if="!isMobile" class="desktop-table-view">
      <el-table 
        :data="data" 
        :loading="loading"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        class="responsive-table"
      >
        <!-- 选择列 -->
        <el-table-column 
          v-if="showSelection"
          type="selection" 
          width="55"
        />
        
        <!-- 动态列 -->
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip !== false"
        >
          <template #default="scope">
            <slot 
              :name="column.prop" 
              :row="scope.row" 
              :column="column"
              :index="scope.$index"
            >
              {{ getCellValue(scope.row, column.prop) }}
            </slot>
          </template>
        </el-table-column>
        
        <!-- 操作列 -->
        <el-table-column 
          v-if="showActions"
          label="操作" 
          :width="actionWidth"
          fixed="right"
        >
          <template #default="scope">
                         <slot 
               name="actions" 
               :row="scope.row" 
               :index="scope.$index"
             >
              <el-button 
                type="text" 
                size="small" 
                @click="$emit('edit', scope.row)"
              >
                编辑
              </el-button>
              <el-button 
                type="text" 
                size="small" 
                @click="$emit('delete', scope.row)"
              >
                删除
              </el-button>
            </slot>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 移动端卡片视图 -->
    <div v-else class="mobile-card-view">
      <div class="mobile-card-list">
        <div 
          v-for="(item, index) in data" 
          :key="getItemKey(item, index)"
          class="mobile-card"
          :class="{ 'selected': selectedItems.includes(item) }"
          @click="handleCardClick(item, index)"
        >
          <!-- 卡片头部 -->
          <div class="card-header">
            <div class="card-title">
              <slot name="card-title" :item="item" :index="index">
                {{ getCardTitle(item) }}
              </slot>
            </div>
            <div class="card-actions">
              <el-dropdown @command="handleCardAction">
                <el-button type="text" class="card-action-btn">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item 
                      :command="{ action: 'edit', item: item }"
                    >
                      编辑
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="{ action: 'delete', item: item }"
                    >
                      删除
                    </el-dropdown-item>
                    <slot name="card-dropdown" :item="item" :index="index" />
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
          
          <!-- 卡片内容 -->
          <div class="card-content">
            <slot name="card-content" :item="item" :index="index">
              <div class="card-fields">
                <div 
                  v-for="column in mobileColumns" 
                  :key="column.prop"
                  class="card-field"
                >
                  <span class="field-label">{{ column.label }}:</span>
                  <span class="field-value">
                    <slot 
                      :name="column.prop" 
                      :row="item" 
                      :column="column"
                      :index="index"
                    >
                      {{ getCellValue(item, column.prop) }}
                    </slot>
                  </span>
                </div>
              </div>
            </slot>
          </div>
          
          <!-- 卡片底部 -->
          <div class="card-footer" v-if="$slots['card-footer']">
            <slot name="card-footer" :item="item" :index="index" />
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div v-if="showPagination" class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="pageSizes"
        :total="total"
        :layout="paginationLayout"
        :small="isMobile"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { MoreFilled } from '@element-plus/icons-vue'

// 定义接口
interface Column {
  prop: string
  label: string
  width?: number
  minWidth?: number
  sortable?: boolean
  showOverflowTooltip?: boolean
  mobileShow?: boolean
}

interface Props {
  data: any[]
  columns: Column[]
  loading?: boolean
  showSelection?: boolean
  showActions?: boolean
  actionWidth?: number
  showPagination?: boolean
  total?: number
  pageSizes?: number[]
  keyField?: string
  titleField?: string
  breakpoint?: number
}

// 定义props
const props = withDefaults(defineProps<Props>(), {
  data: () => [],
  columns: () => [],
  loading: false,
  showSelection: false,
  showActions: true,
  actionWidth: 120,
  showPagination: true,
  total: 0,
  pageSizes: () => [10, 20, 50, 100],
  keyField: 'id',
  titleField: 'name',
  breakpoint: 768
})

// 定义emits
const emit = defineEmits<{
  edit: [item: any]
  delete: [item: any]
  'selection-change': [selection: any[]]
  'sort-change': [sort: any]
  'size-change': [size: number]
  'current-change': [current: number]
}>()

// 响应式状态
const windowWidth = ref(window.innerWidth)
const selectedItems = ref<any[]>([])
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const isMobile = computed(() => windowWidth.value < props.breakpoint)

const mobileColumns = computed(() => 
  props.columns.filter(col => col.mobileShow !== false).slice(0, 4)
)

const paginationLayout = computed(() => 
  isMobile.value 
    ? 'prev, pager, next' 
    : 'total, sizes, prev, pager, next, jumper'
)

// 方法
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

const getCellValue = (row: any, prop: string) => {
  return prop.split('.').reduce((obj, key) => obj?.[key], row) || '-'
}

const getItemKey = (item: any, index: number) => {
  return item[props.keyField] || index
}

const getCardTitle = (item: any) => {
  return item[props.titleField] || `项目 ${item.id}`
}

const handleCardClick = (item: any, index: number) => {
  if (props.showSelection) {
    const selectedIndex = selectedItems.value.findIndex(
      selected => selected[props.keyField] === item[props.keyField]
    )
    
    if (selectedIndex > -1) {
      selectedItems.value.splice(selectedIndex, 1)
    } else {
      selectedItems.value.push(item)
    }
    
    emit('selection-change', selectedItems.value)
  }
}

const handleCardAction = (command: { action: string; item: any }) => {
  if (command.action === 'edit') {
    emit('edit', command.item)
  } else if (command.action === 'delete') {
    emit('delete', command.item)
  }
}

const handleSelectionChange = (selection: any[]) => {
  selectedItems.value = selection
  emit('selection-change', selection)
}

const handleSortChange = (sort: any) => {
  emit('sort-change', sort)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  emit('size-change', size)
}

const handleCurrentChange = (current: number) => {
  currentPage.value = current
  emit('current-change', current)
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.responsive-card-container {
  .desktop-table-view {
    .responsive-table {
      :deep(.el-table__header) {
        background-color: var(--el-bg-color-page);
      }
      
      :deep(.el-table__row) {
        transition: all 0.3s ease;
        
        &:hover {
          background-color: var(--el-fill-color-light);
        }
      }
    }
  }
  
  .mobile-card-view {
    .mobile-card-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }
    
    .mobile-card {
      background: var(--el-bg-color);
      border: 1px solid var(--el-border-color-light);
      border-radius: 8px;
      padding: 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        border-color: var(--el-color-primary);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &.selected {
        border-color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
      }
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        
        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }
        
        .card-actions {
          .card-action-btn {
            padding: 4px;
            color: var(--el-text-color-secondary);
            
            &:hover {
              color: var(--el-color-primary);
            }
          }
        }
      }
      
      .card-content {
        .card-fields {
          display: flex;
          flex-direction: column;
          gap: 8px;
          
          .card-field {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .field-label {
              font-size: 14px;
              color: var(--el-text-color-secondary);
              min-width: 80px;
            }
            
            .field-value {
              font-size: 14px;
              color: var(--el-text-color-primary);
              text-align: right;
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
      
      .card-footer {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid var(--el-border-color-lighter);
      }
    }
  }
  
  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    
    :deep(.el-pagination) {
      &.el-pagination--small {
        .el-pagination__editor {
          height: 28px;
        }
      }
    }
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .responsive-card-container {
    .mobile-card-view {
      .mobile-card {
        &:hover {
          box-shadow: 0 2px 8px rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
}

// 移动端样式优化
@media (max-width: 768px) {
  .responsive-card-container {
    .pagination-container {
      margin-top: 16px;
      
      :deep(.el-pagination) {
        justify-content: center;
        
        .el-pagination__total,
        .el-pagination__sizes {
          display: none;
        }
      }
    }
  }
}
</style> 