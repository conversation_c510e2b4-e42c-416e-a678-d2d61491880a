<template>
  <div class="mobile-optimization-wrapper">
    <!-- 移动端导航栏 -->
    <div class="mobile-nav" v-if="isMobile">
      <div class="nav-header">
        <div class="nav-title">
          <slot name="title">CMDB管理</slot>
        </div>
        <div class="nav-actions">
          <el-button 
            type="text" 
            @click="$emit('refresh')"
            :loading="loading"
            class="nav-btn"
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button 
            type="text" 
            @click="$emit('add')"
            class="nav-btn"
          >
            <el-icon><Plus /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 移动端搜索栏 -->
    <div class="mobile-search" v-if="isMobile && showSearch">
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索..."
          clearable
          @input="handleSearch"
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 移动端筛选器 -->
    <div class="mobile-filters" v-if="isMobile && filters.length > 0">
      <el-scrollbar>
        <div class="filter-chips">
          <el-tag
            v-for="filter in filters"
            :key="filter.key"
            :type="filter.active ? 'primary' : 'info'"
            :effect="filter.active ? 'dark' : 'light'"
            @click="handleFilterClick(filter)"
            class="filter-chip"
          >
            {{ filter.label }}
          </el-tag>
        </div>
      </el-scrollbar>
    </div>

    <!-- 主要内容区域 -->
    <div class="mobile-content" :class="{ 'with-mobile-nav': isMobile }">
      <slot />
    </div>

    <!-- 移动端浮动操作按钮 -->
    <div class="mobile-fab" v-if="isMobile && showFab">
      <el-button
        type="primary"
        :icon="Plus"
        circle
        size="large"
        @click="$emit('add')"
        class="fab-button"
      />
    </div>

    <!-- 移动端手势支持 -->
    <div 
      class="gesture-overlay"
      v-if="isMobile && enableGestures"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    />

    <!-- 移动端安全区域适配 -->
    <div class="safe-area-bottom" v-if="isMobile" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Refresh, Plus, Search } from '@element-plus/icons-vue'

// 定义接口
interface Filter {
  key: string
  label: string
  active: boolean
}

interface Props {
  loading?: boolean
  showSearch?: boolean
  showFab?: boolean
  enableGestures?: boolean
  filters?: Filter[]
  breakpoint?: number
}

// 定义props
const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showSearch: true,
  showFab: true,
  enableGestures: true,
  filters: () => [],
  breakpoint: 768
})

// 定义emits
const emit = defineEmits<{
  refresh: []
  add: []
  search: [query: string]
  filter: [filter: Filter]
  swipeLeft: []
  swipeRight: []
  swipeUp: []
  swipeDown: []
}>()

// 响应式状态
const windowWidth = ref(window.innerWidth)
const searchQuery = ref('')
const touchStartX = ref(0)
const touchStartY = ref(0)
const touchEndX = ref(0)
const touchEndY = ref(0)

// 计算属性
const isMobile = computed(() => windowWidth.value < props.breakpoint)

// 方法
const handleResize = () => {
  windowWidth.value = window.innerWidth
}

const handleSearch = () => {
  emit('search', searchQuery.value)
}

const handleFilterClick = (filter: Filter) => {
  filter.active = !filter.active
  emit('filter', filter)
}

// 手势处理
const handleTouchStart = (e: TouchEvent) => {
  if (!props.enableGestures) return
  
  const touch = e.touches[0]
  touchStartX.value = touch.clientX
  touchStartY.value = touch.clientY
}

const handleTouchMove = (e: TouchEvent) => {
  if (!props.enableGestures) return
  
  // 阻止默认滚动行为（可选）
  // e.preventDefault()
}

const handleTouchEnd = (e: TouchEvent) => {
  if (!props.enableGestures) return
  
  const touch = e.changedTouches[0]
  touchEndX.value = touch.clientX
  touchEndY.value = touch.clientY
  
  handleSwipeGesture()
}

const handleSwipeGesture = () => {
  const deltaX = touchEndX.value - touchStartX.value
  const deltaY = touchEndY.value - touchStartY.value
  const minSwipeDistance = 50
  
  if (Math.abs(deltaX) > Math.abs(deltaY)) {
    // 水平滑动
    if (Math.abs(deltaX) > minSwipeDistance) {
      if (deltaX > 0) {
        emit('swipeRight')
      } else {
        emit('swipeLeft')
      }
    }
  } else {
    // 垂直滑动
    if (Math.abs(deltaY) > minSwipeDistance) {
      if (deltaY > 0) {
        emit('swipeDown')
      } else {
        emit('swipeUp')
      }
    }
  }
}

// 生命周期
onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // 设置viewport meta标签以支持移动端
  const viewport = document.querySelector('meta[name="viewport"]')
  if (!viewport) {
    const meta = document.createElement('meta')
    meta.name = 'viewport'
    meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover'
    document.head.appendChild(meta)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped lang="scss">
.mobile-optimization-wrapper {
  position: relative;
  min-height: 100vh;
  
  .mobile-nav {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--el-bg-color);
    border-bottom: 1px solid var(--el-border-color-light);
    backdrop-filter: blur(10px);
    
    // 安全区域适配
    padding-top: env(safe-area-inset-top);
    
    .nav-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      
      .nav-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
      }
      
      .nav-actions {
        display: flex;
        gap: 8px;
        
        .nav-btn {
          padding: 8px;
          color: var(--el-text-color-primary);
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
  
  .mobile-search {
    position: sticky;
    top: 60px;
    z-index: 999;
    background: var(--el-bg-color);
    padding: 12px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .search-container {
      .search-input {
        border-radius: 20px;
        
        :deep(.el-input__wrapper) {
          border-radius: 20px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .mobile-filters {
    position: sticky;
    top: 120px;
    z-index: 998;
    background: var(--el-bg-color);
    padding: 8px 16px;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    .filter-chips {
      display: flex;
      gap: 8px;
      padding: 4px 0;
      
      .filter-chip {
        cursor: pointer;
        transition: all 0.3s ease;
        flex-shrink: 0;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
  
  .mobile-content {
    padding: 16px;
    padding-bottom: 80px; // 为FAB留出空间
    
    &.with-mobile-nav {
      padding-top: 80px; // 为导航栏留出空间
    }
  }
  
  .mobile-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1001;
    
    // 安全区域适配
    bottom: calc(20px + env(safe-area-inset-bottom));
    
    .fab-button {
      width: 56px;
      height: 56px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      
      &:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
      }
      
      &:active {
        transform: scale(0.95);
      }
    }
  }
  
  .gesture-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
  }
  
  .safe-area-bottom {
    height: env(safe-area-inset-bottom);
    background: var(--el-bg-color);
  }
}

// 暗色主题适配
@media (prefers-color-scheme: dark) {
  .mobile-optimization-wrapper {
    .mobile-nav {
      background: var(--el-bg-color);
      border-bottom-color: var(--el-border-color);
    }
    
    .mobile-search {
      background: var(--el-bg-color);
      border-bottom-color: var(--el-border-color);
    }
    
    .mobile-filters {
      background: var(--el-bg-color);
      border-bottom-color: var(--el-border-color);
    }
  }
}

// 触摸优化
@media (hover: none) and (pointer: coarse) {
  .mobile-optimization-wrapper {
    // 增加触摸目标大小
    .nav-btn,
    .filter-chip,
    .fab-button {
      min-height: 44px;
      min-width: 44px;
    }
    
    // 优化触摸反馈
    .filter-chip:active {
      transform: scale(0.95);
    }
    
    .fab-button:active {
      transform: scale(0.9);
    }
  }
}

// 横屏适配
@media (orientation: landscape) and (max-height: 500px) {
  .mobile-optimization-wrapper {
    .mobile-nav {
      .nav-header {
        padding: 8px 16px;
      }
    }
    
    .mobile-search {
      padding: 8px 16px;
    }
    
    .mobile-filters {
      padding: 4px 16px;
    }
  }
}

// 大屏幕隐藏移动端元素
@media (min-width: 768px) {
  .mobile-optimization-wrapper {
    .mobile-nav,
    .mobile-search,
    .mobile-filters,
    .mobile-fab,
    .safe-area-bottom {
      display: none;
    }
    
    .mobile-content {
      padding: 0;
      
      &.with-mobile-nav {
        padding-top: 0;
      }
    }
  }
}

// 高分辨率屏幕优化
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-optimization-wrapper {
    .mobile-nav {
      border-bottom-width: 0.5px;
    }
    
    .mobile-search,
    .mobile-filters {
      border-bottom-width: 0.5px;
    }
  }
}
</style> 