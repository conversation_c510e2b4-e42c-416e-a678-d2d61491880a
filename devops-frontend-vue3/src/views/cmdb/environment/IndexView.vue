<template>
  <div class="environment-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h3>环境管理</h3>
        <p class="page-description">管理系统部署环境配置，包括开发、测试、生产等环境设置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate" v-permission="'environment:create'">
          <el-icon><Plus /></el-icon>
          新建环境
        </el-button>
      </div>
    </div>
    
    <!-- 搜索筛选区 -->
    <el-card class="search-card" shadow="hover">
      <div class="search-form-wrapper">
        <el-form :model="searchForm" inline class="search-form">
          <el-form-item label="环境名称">
            <el-input 
              v-model="searchForm.name"
              placeholder="请输入环境名称"
              clearable
              @keyup.enter="handleSearch"
              style="width: 200px"
            />
          </el-form-item>
          
          <el-form-item label="工单启用">
            <el-select 
              v-model="searchForm.ticket_enabled"
              placeholder="请选择"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="合并启用">
            <el-select 
              v-model="searchForm.merge_enabled"
              placeholder="请选择"
              clearable
              style="width: 120px"
            >
              <el-option label="启用" :value="1" />
              <el-option label="禁用" :value="0" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <!-- 视图模式切换 -->
    <div class="view-mode-controls">
      <div class="view-mode-switch">
        <el-radio-group v-model="viewMode" size="small">
          <el-radio-button label="card">
            <el-icon><Grid /></el-icon>
            卡片视图
          </el-radio-button>
          <el-radio-button label="table">
            <el-icon><List /></el-icon>
            表格视图
          </el-radio-button>
        </el-radio-group>
      </div>
      
      <div class="view-stats">
        <el-text type="info">
          共 {{ pagination.total }} 个环境
        </el-text>
      </div>
    </div>
    
    <!-- 环境卡片列表视图 -->
    <div class="env-card-list" v-if="viewMode === 'card'" v-loading="loading">
      <el-row :gutter="24">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="env in environments" :key="env.id" class="env-card-col">
          <el-card class="env-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="env-title">
                  <span class="env-name">{{ env.name || env.environment_code }}</span>
                  <el-tag size="small" type="info">{{ env.name }}</el-tag>
                </div>
                <div class="card-operations">
                  <el-dropdown trigger="click" @command="handleCardCommand($event, env)">
                    <el-button type="primary" text>
                      <el-icon><More /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="edit" v-permission="'environment:update'">
                          <el-icon><Edit /></el-icon>
                          编辑
                        </el-dropdown-item>
                        <el-dropdown-item command="view">
                          <el-icon><View /></el-icon>
                          查看详情
                        </el-dropdown-item>
                        <el-dropdown-item command="delete" divided v-permission="'environment:delete'">
                          <el-icon><Delete /></el-icon>
                          删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </template>
            
            <div class="env-info">
              <div class="info-row">
                <div class="info-item">
                  <span class="label">工单开关：</span>
                  <el-switch
                    v-model="env.ticket_enabled"
                    disabled
                    :active-value="1"
                    :inactive-value="0"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </div>
                
                <div class="info-item">
                  <span class="label">合并开关：</span>
                  <el-switch
                    v-model="env.merge_enabled"
                    disabled
                    :active-value="1"
                    :inactive-value="0"
                    active-color="#13ce66"
                    inactive-color="#ff4949"
                  />
                </div>
              </div>
              
              <div class="info-item">
                <span class="label">排序：</span>
                <el-tag type="warning" size="small">{{ env.sort_order }}</el-tag>
              </div>
              
              <div class="info-item" v-if="env.description">
                <span class="label">描述：</span>
                <span class="value">{{ env.description }}</span>
              </div>
            </div>
            
            <el-divider />
            
            <div class="env-config">
              <div class="config-section">
                <div class="config-title">
                  <el-icon><Setting /></el-icon>
                  配置信息
                </div>
                
                <div class="config-item" v-if="env.template_settings">
                  <span class="label">模板设置：</span>
                  <div class="config-content">
                    <el-text type="success" v-if="hasTemplateSettings(env)">已配置</el-text>
                    <el-text type="info" v-else>未配置</el-text>
                  </div>
                </div>
                
                <div class="config-item" v-if="env.allow_ci_branch">
                  <span class="label">CI分支：</span>
                  <div class="tags-container">
                    <template v-if="getCIBranches(env).length">
                      <el-tag 
                        v-for="branch in getCIBranches(env).slice(0, 3)" 
                        :key="branch" 
                        size="small"
                        type="success"
                      >
                        {{ branch }}
                      </el-tag>
                      <el-tag v-if="getCIBranches(env).length > 3" type="info" size="small">
                        +{{ getCIBranches(env).length - 3 }}
                      </el-tag>
                    </template>
                    <el-text type="info" v-else>未配置</el-text>
                  </div>
                </div>
                
                <div class="config-item" v-if="env.allow_cd_branch">
                  <span class="label">CD分支：</span>
                  <div class="tags-container">
                    <template v-if="getCDBranches(env).length">
                      <el-tag 
                        v-for="branch in getCDBranches(env).slice(0, 3)" 
                        :key="branch" 
                        size="small"
                        type="warning"
                      >
                        {{ branch }}
                      </el-tag>
                      <el-tag v-if="getCDBranches(env).length > 3" type="info" size="small">
                        +{{ getCDBranches(env).length - 3 }}
                      </el-tag>
                    </template>
                    <el-text type="info" v-else>未配置</el-text>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="card-footer">
              <div class="time-info">
                <el-text type="info" size="small">
                  创建时间：{{ formatDate(env.created_at) }}
                </el-text>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      
      <!-- 空状态 -->
      <el-empty v-if="!loading && environments.length === 0" description="暂无环境数据">
        <el-button type="primary" @click="handleCreate" v-permission="'environment:create'">
          创建第一个环境
        </el-button>
      </el-empty>
    </div>
    
    <!-- 环境表格视图 -->
    <el-card class="table-card" v-if="viewMode === 'table'" shadow="never">
      <el-table 
        :data="environments" 
        v-loading="loading"
        border
        stripe
        row-key="id"
        :default-sort="{ prop: 'sort_order', order: 'ascending' }"
      >
        <el-table-column prop="id" label="ID" width="80" sortable />
        
        <el-table-column prop="environment_code" label="环境代码" width="150" sortable>
          <template #default="{ row }">
            <span>{{ row.environment_code || '-' }}</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="name" label="环境名称" width="120" sortable>
          <template #default="{ row }">
            <el-text type="primary" style="font-weight: 500;">{{ row.name }}</el-text>
          </template>
        </el-table-column>
        
        <el-table-column label="工单开关" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.ticket_enabled"
              disabled
              :active-value="1"
              :inactive-value="0"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>
        
        <el-table-column label="合并开关" width="100" align="center">
          <template #default="{ row }">
            <el-switch
              v-model="row.merge_enabled"
              disabled
              :active-value="1"
              :inactive-value="0"
              active-color="#13ce66"
              inactive-color="#ff4949"
            />
          </template>
        </el-table-column>
        
        <el-table-column prop="sort_order" label="排序" width="80" align="center" sortable>
          <template #default="{ row }">
            <el-tag type="warning" size="small">{{ row.sort_order }}</el-tag>
          </template>
        </el-table-column>
        
        <el-table-column label="分支配置" width="200">
          <template #default="{ row }">
            <div class="branch-config">
              <div class="branch-item" v-if="getCIBranches(row).length">
                <el-text type="success" size="small">CI: {{ getCIBranches(row).slice(0, 2).join(', ') }}</el-text>
                <el-text v-if="getCIBranches(row).length > 2" type="info" size="small">...</el-text>
              </div>
              <div class="branch-item" v-if="getCDBranches(row).length">
                <el-text type="warning" size="small">CD: {{ getCDBranches(row).slice(0, 2).join(', ') }}</el-text>
                <el-text v-if="getCDBranches(row).length > 2" type="info" size="small">...</el-text>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        
        <el-table-column prop="created_at" label="创建时间" width="180" sortable>
          <template #default="{ row }">
            <span>{{ formatDate(row.created_at) }}</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="table-operations">
              <el-button size="small" type="primary" @click="handleEdit(row)" v-permission="'environment:update'">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button size="small" type="info" @click="handleView(row)">
                <el-icon><View /></el-icon>
                详情
              </el-button>
              <el-button size="small" type="danger" @click="handleDelete(row)" v-permission="'environment:delete'">
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          :current-page="pagination.page"
          :page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    
    <!-- 新增/编辑环境对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'create' ? '新增环境' : '编辑环境'"
      width="800px"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="环境代码" prop="environment_code">
              <el-input v-model="form.environment_code" placeholder="请输入环境代码，如：dev" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="环境名称" prop="name">
              <el-input 
                v-model="form.name" 
                placeholder="请输入环境名称，如：dev"
                :disabled="dialogType === 'edit'"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="排序" prop="sort_order">
              <el-input-number 
                v-model="form.sort_order" 
                :min="1" 
                :max="999" 
                controls-position="right"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工单开关" prop="ticket_enabled">
              <el-switch
                v-model="form.ticket_enabled"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">
                <el-text type="info" size="small">启用后该环境需要工单审批</el-text>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="合并开关" prop="merge_enabled">
              <el-switch
                v-model="form.merge_enabled"
                :active-value="1"
                :inactive-value="0"
                active-text="启用"
                inactive-text="禁用"
              />
              <div class="form-tip">
                <el-text type="info" size="small">启用后支持代码合并部署</el-text>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入环境描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <!-- CI分支配置 -->
        <el-form-item label="CI分支">
          <el-select
            v-model="form.allow_ci_branch_list"
            multiple
            filterable
            allow-create
            placeholder="选择或输入CI分支"
            style="width: 100%"
          >
            <el-option
              v-for="branch in defaultBranches"
              :key="branch"
              :label="branch"
              :value="branch"
            />
          </el-select>
          <div class="form-tip">
            <el-text type="info" size="small">设置允许进行持续集成的分支</el-text>
          </div>
        </el-form-item>

        <!-- CD分支配置 -->
        <el-form-item label="CD分支">
          <el-select
            v-model="form.allow_cd_branch_list"
            multiple
            filterable
            allow-create
            placeholder="选择或输入CD分支"
            style="width: 100%"
          >
            <el-option
              v-for="branch in defaultBranches"
              :key="branch"
              :label="branch"
              :value="branch"
            />
          </el-select>
          <div class="form-tip">
            <el-text type="info" size="small">设置允许进行持续部署的分支</el-text>
          </div>
        </el-form-item>

        <!-- 模板设置 -->
        <el-form-item label="模板设置">
          <el-input 
            v-model="form.template_settings_json"
            type="textarea"
            :rows="4"
            placeholder="请输入JSON格式的模板设置，如：{&quot;dockerfile&quot;: &quot;template&quot;}"
          />
          <div class="form-tip">
            <el-text type="info" size="small">JSON格式的模板配置信息</el-text>
          </div>
        </el-form-item>

        <!-- 扩展信息 -->
        <el-form-item label="扩展信息">
          <el-input 
            v-model="form.extra_data_json"
            type="textarea"
            :rows="3"
            placeholder="请输入JSON格式的扩展信息"
          />
          <div class="form-tip">
            <el-text type="info" size="small">JSON格式的扩展配置信息</el-text>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ dialogType === 'create' ? '创建' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 环境详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="环境详情"
      width="700px"
      destroy-on-close
    >
      <div class="detail-content" v-if="currentEnvironment">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="环境代码">{{ currentEnvironment.environment_code || '-' }}</el-descriptions-item>
          <el-descriptions-item label="环境名称">{{ currentEnvironment.name }}</el-descriptions-item>
          <el-descriptions-item label="排序">{{ currentEnvironment.sort_order }}</el-descriptions-item>
          <el-descriptions-item label="工单开关">
            <el-tag :type="currentEnvironment.ticket_enabled === 1 ? 'success' : 'danger'">
              {{ currentEnvironment.ticket_enabled === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="合并开关">
            <el-tag :type="currentEnvironment.merge_enabled === 1 ? 'success' : 'danger'">
              {{ currentEnvironment.merge_enabled === 1 ? '启用' : '禁用' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentEnvironment.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDate(currentEnvironment.updated_at) }}</el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ currentEnvironment.description || '-' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="config-details">
          <div class="config-section" v-if="getCIBranches(currentEnvironment).length">
            <h4>CI分支配置</h4>
            <div class="branch-tags">
              <el-tag 
                v-for="branch in getCIBranches(currentEnvironment)" 
                :key="branch" 
                type="success"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ branch }}
              </el-tag>
            </div>
          </div>

          <div class="config-section" v-if="getCDBranches(currentEnvironment).length">
            <h4>CD分支配置</h4>
            <div class="branch-tags">
              <el-tag 
                v-for="branch in getCDBranches(currentEnvironment)" 
                :key="branch" 
                type="warning"
                style="margin-right: 8px; margin-bottom: 8px;"
              >
                {{ branch }}
              </el-tag>
            </div>
          </div>

          <div class="config-section" v-if="currentEnvironment.template_settings">
            <h4>模板设置</h4>
            <el-input 
              :model-value="formatJSON(currentEnvironment.template_settings)"
              type="textarea"
              :rows="6"
              readonly
            />
          </div>

          <div class="config-section" v-if="currentEnvironment.extra_data">
            <h4>扩展信息</h4>
            <el-input 
              :model-value="formatJSON(currentEnvironment.extra_data)"
              type="textarea"
              :rows="4"
              readonly
            />
          </div>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEdit(currentEnvironment)" v-permission="'environment:update'" v-if="currentEnvironment">
            编辑环境
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus, Edit, Delete, View, More, Search, Refresh,
  Grid, List, Setting
} from '@element-plus/icons-vue'
import ResponsiveCard from '../components/ResponsiveCard.vue'
import MobileOptimization from '../components/MobileOptimization.vue'
import { environmentApi, type Environment, type EnvironmentCreateRequest } from '../../../api/modules/cmdb'
import { handleAPIResponse } from '../../../utils/response'
import { formatDate } from '../../../utils/date'

// 定义状态
const loading = ref(false)
const submitting = ref(false)
const viewMode = ref<'card' | 'table'>('card')
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const dialogType = ref<'create' | 'edit'>('create')

// 分页配置
const pagination = reactive({
  page: 1,
  page_size: 20,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  name: '',
  ticket_enabled: undefined as number | undefined,
  merge_enabled: undefined as number | undefined
})

// 环境表单
const formRef = ref()
const form = reactive({
  id: 0,
  name: '',
  environment_code: '',
  sort_order: 999,
  ticket_enabled: 1,
  merge_enabled: 0,
  template_settings: null as any,
  allow_ci_branch: null as any,
  allow_cd_branch: null as any,
  extra_data: null as any,
  description: '',
  // 辅助字段，用于表单编辑
  template_settings_json: '',
  extra_data_json: '',
  allow_ci_branch_list: [] as string[],
  allow_cd_branch_list: [] as string[]
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入环境名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  environment_code: [
    { required: true, message: '请输入环境代码', trigger: 'blur' },
    { max: 128, message: '长度不能超过 128 个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  sort_order: [
    { required: true, message: '请输入排序值', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '排序值必须在 1-999 之间', trigger: 'blur' }
  ]
}

// 环境数据
const environments = ref<Environment[]>([])
const currentEnvironment = ref<Environment | null>(null)

// 默认分支选项
const defaultBranches = [
  'master', 'main', 'develop', 'dev', 'test', 'staging', 'production', 'prod',
  'release', 'hotfix', 'feature', 'bugfix'
]

// 工具函数
const hasTemplateSettings = (env: Environment) => {
  return env.template_settings && 
         typeof env.template_settings === 'object' && 
         Object.keys(env.template_settings).length > 0
}

const getCIBranches = (env: Environment): string[] => {
  if (!env.allow_ci_branch) return []
  if (Array.isArray(env.allow_ci_branch)) return env.allow_ci_branch
  if (typeof env.allow_ci_branch === 'object' && env.allow_ci_branch.branches) {
    return env.allow_ci_branch.branches
  }
  return []
}

const getCDBranches = (env: Environment): string[] => {
  if (!env.allow_cd_branch) return []
  if (Array.isArray(env.allow_cd_branch)) return env.allow_cd_branch
  if (typeof env.allow_cd_branch === 'object' && env.allow_cd_branch.branches) {
    return env.allow_cd_branch.branches
  }
  return []
}

const formatJSON = (obj: any): string => {
  if (!obj) return ''
  try {
    return JSON.stringify(obj, null, 2)
  } catch {
    return String(obj)
  }
}

const parseJSON = (jsonStr: string) => {
  if (!jsonStr || jsonStr.trim() === '') return null
  try {
    return JSON.parse(jsonStr)
  } catch {
    return null
  }
}

// 获取环境列表
const getEnvironmentList = async () => {
  loading.value = true
  try {
    const params = {
      name: searchForm.name || undefined,
      page: pagination.page,
      page_size: pagination.page_size
    }
    
    const response = await environmentApi.getEnvironments(params)
    const result = handleAPIResponse(response) as any
    
    // 统一处理列表响应格式
    if (result.items) {
      environments.value = result.items
      pagination.total = result.total || 0
    } else if (Array.isArray(result)) {
      environments.value = result
      pagination.total = result.length
    } else {
      environments.value = []
      pagination.total = 0
    }
  } catch (error: any) {
    console.error('获取环境列表失败:', error)
    ElMessage.error(error.message || '获取环境列表失败')
  } finally {
    loading.value = false
  }
}

// 事件处理方法
const handleSearch = () => {
  pagination.page = 1
  getEnvironmentList()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.ticket_enabled = undefined
  searchForm.merge_enabled = undefined
  handleSearch()
}

const handleCreate = () => {
  dialogType.value = 'create'
  resetForm()
  dialogVisible.value = true
}

const resetForm = () => {
  Object.assign(form, {
    id: 0,
    name: '',
    environment_code: '',
    sort_order: 999,
    ticket_enabled: 1,
    merge_enabled: 0,
    template_settings: null,
    allow_ci_branch: null,
    allow_cd_branch: null,
    extra_data: null,
    description: '',
    template_settings_json: '',
    extra_data_json: '',
    allow_ci_branch_list: [],
    allow_cd_branch_list: []
  })
  
  // 清除表单验证
  formRef.value?.clearValidate()
}

const handleEdit = async (row: Environment) => {
  dialogType.value = 'edit'
  try {
    const response = await environmentApi.getEnvironment(row.id)
    const environment = handleAPIResponse(response) as Environment
    
    Object.assign(form, {
      id: environment.id,
      name: environment.name,
      environment_code: environment.environment_code || '',
      sort_order: environment.sort_order || 999,
      ticket_enabled: environment.ticket_enabled,
      merge_enabled: environment.merge_enabled,
      template_settings: environment.template_settings,
      allow_ci_branch: environment.allow_ci_branch,
      allow_cd_branch: environment.allow_cd_branch,
      extra_data: environment.extra_data,
      description: environment.description || '',
      template_settings_json: formatJSON(environment.template_settings),
      extra_data_json: formatJSON(environment.extra_data),
      allow_ci_branch_list: getCIBranches(environment),
      allow_cd_branch_list: getCDBranches(environment)
    })
    
    dialogVisible.value = true
  } catch (error: any) {
    console.error('获取环境详情失败:', error)
    ElMessage.error(error.message || '获取环境详情失败')
  }
}

const handleView = async (row: Environment) => {
  try {
    const response = await environmentApi.getEnvironment(row.id)
    currentEnvironment.value = handleAPIResponse(response)
    detailDialogVisible.value = true
  } catch (error: any) {
    console.error('获取环境详情失败:', error)
    ElMessage.error(error.message || '获取环境详情失败')
  }
}

const handleDelete = async (row: Environment) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除环境 "${row.name || row.environment_code}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await environmentApi.deleteEnvironment(row.id)
    handleAPIResponse(response)
    
    ElMessage.success('删除成功')
    getEnvironmentList()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除环境失败:', error)
      ElMessage.error(error.message || '删除环境失败')
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 准备请求数据
    const requestData: EnvironmentCreateRequest = {
      name: form.name,
      environment_code: form.environment_code || undefined,
      sort_order: form.sort_order,
      ticket_enabled: form.ticket_enabled,
      merge_enabled: form.merge_enabled,
      description: form.description || undefined,
      template_settings: parseJSON(form.template_settings_json),
      extra_data: parseJSON(form.extra_data_json),
      allow_ci_branch: form.allow_ci_branch_list.length > 0 ? form.allow_ci_branch_list : undefined,
      allow_cd_branch: form.allow_cd_branch_list.length > 0 ? form.allow_cd_branch_list : undefined
    }
    
    let response
    if (dialogType.value === 'create') {
      response = await environmentApi.createEnvironment(requestData)
    } else {
      response = await environmentApi.updateEnvironment(form.id, requestData)
    }
    
    handleAPIResponse(response)
    
    ElMessage.success(dialogType.value === 'create' ? '创建成功' : '更新成功')
    dialogVisible.value = false
    getEnvironmentList()
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error(error.message || '提交失败')
  } finally {
    submitting.value = false
  }
}

const handleCardCommand = (command: string | Event, env?: Environment) => {
  if (typeof command === 'string' && env) {
    switch (command) {
      case 'edit':
        handleEdit(env)
        break
      case 'view':
        handleView(env)
        break
      case 'delete':
        handleDelete(env)
        break
    }
  }
}

const handleSizeChange = (val: number) => {
  pagination.page_size = val
  pagination.page = 1
  getEnvironmentList()
}

const handleCurrentChange = (val: number) => {
  pagination.page = val
  getEnvironmentList()
}

// 生命周期
onMounted(() => {
  getEnvironmentList()
})
</script>

<style scoped>
.environment-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 120px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left h3 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.search-form-wrapper {
  padding: 10px 0;
}

.view-mode-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.env-card-list {
  min-height: 200px;
}

.env-card-col {
  margin-bottom: 20px;
}

.env-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  height: 100%;
}

.env-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.env-title {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.env-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.env-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  gap: 20px;
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.info-item .label {
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
}

.info-item .value {
  color: #303133;
  font-size: 14px;
}

.env-config {
  margin-top: 16px;
}

.config-section {
  margin-bottom: 16px;
}

.config-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 12px;
  font-weight: 500;
  color: #409eff;
}

.config-item {
  margin-bottom: 12px;
}

.config-item .label {
  display: block;
  margin-bottom: 6px;
  color: #606266;
  font-size: 13px;
}

.config-content {
  margin-top: 4px;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
}

.card-footer {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.time-info {
  text-align: right;
}

.table-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.branch-config {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.branch-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.table-operations {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.form-tip {
  margin-top: 4px;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.config-details {
  margin-top: 20px;
}

.config-details .config-section {
  margin-bottom: 24px;
}

.config-details h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.branch-tags {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .env-card-col {
    --el-col-span: 12;
  }
}

@media (max-width: 768px) {
  .environment-page {
    padding: 10px;
  }
  
  .env-card-col {
    --el-col-span: 24;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .view-mode-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .table-operations {
    flex-direction: column;
  }
}

/* 暗色主题适配 */
.dark .environment-page {
  background-color: #141414;
}

.dark .page-header {
  background-color: #1f1f1f;
  border: 1px solid #303030;
}

.dark .view-mode-controls {
  background-color: #1f1f1f;
  border: 1px solid #303030;
}
</style> 