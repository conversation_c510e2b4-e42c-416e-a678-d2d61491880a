<template>
  <div class="product-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-left">
        <h3>产品管理</h3>
        <p class="page-description">管理系统中的产品信息，包括产品基本信息、区域分配和管理员配置</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate" :icon="Plus">
          新建产品
        </el-button>
      </div>
    </div>
    
    <!-- 搜索筛选区 -->
    <el-card class="search-card" shadow="never">
      <div class="search-form-wrapper">
        <el-form
          ref="searchFormRef"
          :model="searchForm"
          class="search-form"
          :inline="true"
          label-width="80px"
        >
          <el-form-item label="产品名称" prop="name">
            <el-input 
              v-model="searchForm.name" 
              placeholder="请输入产品名称"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="区域" prop="region_id">
            <el-select 
              v-model="searchForm.region_id" 
              placeholder="请选择区域"
              clearable
              style="width: 200px"
            >
              <el-option 
                v-for="region in regionOptions" 
                :key="region.id" 
                :label="region.name || region.region_code" 
                :value="region.id" 
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :icon="Search">
              搜索
            </el-button>
            <el-button @click="handleReset" :icon="Refresh">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 数据表格/卡片 -->
    <el-card shadow="never" class="table-card">
      <template #header>
        <div class="table-header">
          <div class="table-title">
            <span>产品列表</span>
            <el-tag type="info" size="small" class="ml-2">
              共 {{ pagination.total }} 条
            </el-tag>
          </div>
          <div class="table-actions">
            <el-button 
              type="success" 
              size="small" 
              @click="handleExport"
              :icon="Download"
            >
              导出
            </el-button>
          </div>
        </div>
      </template>

      <!-- PC端表格 -->
      <div class="desktop-table">
        <el-table
          :data="tableData"
          v-loading="loading"
          border
          stripe
          style="width: 100%"
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="产品名称" min-width="150">
            <template #default="{ row }">
              <div class="product-name">
                <span class="name">{{ row.name }}</span>
                <span v-if="row.product_code" class="display-name"> | {{ row.product_code }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="region" label="所属区域" width="120">
            <template #default="{ row }">
              <el-tag v-if="row.region" type="success" size="small">
                {{ row.region.name || row.region.region_code }}
              </el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="name_prefix" label="名称前缀" width="100">
            <template #default="{ row }">
              <el-tag v-if="row.name_prefix" size="small">{{ row.name_prefix }}</el-tag>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="managers" label="产品负责人" width="150">
            <template #default="{ row }">
              <div v-if="row.managers && row.managers.manager" class="manager-list">
                <el-tag 
                  v-for="manager in (Array.isArray(row.managers.manager) ? row.managers.manager : [row.managers.manager])" 
                  :key="manager" 
                  size="small" 
                  class="manager-tag"
                >
                  {{ getUserDisplayName(manager) }}
                </el-tag>
              </div>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="tech_manager" label="技术负责人" width="150">
            <template #default="{ row }">
              <div v-if="row.managers && row.managers.tech_manager" class="manager-list">
                <el-tag 
                  v-for="manager in (Array.isArray(row.managers.tech_manager) ? row.managers.tech_manager : [row.managers.tech_manager])" 
                  :key="manager" 
                  size="small" 
                  class="manager-tag"
                  type="warning"
                >
                  {{ getUserDisplayName(manager) }}
                </el-tag>
              </div>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-tooltip content="查看详情" placement="top">
                  <el-button 
                    type="primary" 
                    size="small" 
                    :icon="View" 
                    circle
                    @click="handleView(row)"
                  />
                </el-tooltip>
                <el-tooltip content="编辑" placement="top">
                  <el-button 
                    type="warning" 
                    size="small" 
                    :icon="Edit" 
                    circle
                    @click="handleEdit(row)"
                  />
                </el-tooltip>
                <el-tooltip content="删除" placement="top">
                  <el-button 
                    type="danger" 
                    size="small" 
                    :icon="Delete" 
                    circle
                    @click="handleDelete(row)"
                  />
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 移动端卡片 -->
      <div class="mobile-cards">
        <div v-if="tableData.length === 0" class="empty-state">
          <el-empty description="暂无产品数据">
            <el-button type="primary" @click="handleCreate" :icon="Plus">
              新建产品
            </el-button>
          </el-empty>
        </div>
        
        <div v-else v-loading="loading">
          <el-card 
            v-for="product in tableData" 
            :key="product.id" 
            class="product-card"
            shadow="hover"
          >
            <template #header>
              <div class="card-header">
                <div class="card-title">
                  <span class="product-id">ID: {{ product.id }}</span>
                  <span class="product-name">{{ product.name }}</span>
                  <span v-if="product.product_code" class="product-code">{{ product.product_code }}</span>
                </div>
                <el-dropdown trigger="click">
                  <el-button type="text" class="more-btn">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleView(product)">
                        <el-icon><View /></el-icon>
                        查看详情
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleEdit(product)">
                        <el-icon><Edit /></el-icon>
                        编辑
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleDelete(product)" divided>
                        <el-icon><Delete /></el-icon>
                        删除
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>

            <div class="card-content">
              <div class="info-item">
                <span class="label">所属区域:</span>
                <div class="value">
                  <el-tag v-if="product.region" type="success" size="small">
                    {{ product.region.name || product.region.region_code }}
                  </el-tag>
                  <span v-else class="text-muted">-</span>
                </div>
              </div>

              <div class="info-item">
                <span class="label">名称前缀:</span>
                <div class="value">
                  <el-tag v-if="product.name_prefix" size="small">{{ product.name_prefix }}</el-tag>
                  <span v-else class="text-muted">-</span>
                </div>
              </div>

              <div class="info-item">
                <span class="label">产品负责人:</span>
                <div class="value">
                  <div v-if="product.managers && product.managers.manager" class="manager-list">
                    <el-tag 
                      v-for="manager in (Array.isArray(product.managers.manager) ? product.managers.manager : [product.managers.manager])" 
                      :key="manager" 
                      size="small" 
                      class="manager-tag"
                    >
                      {{ getUserDisplayName(manager) }}
                    </el-tag>
                  </div>
                  <span v-else class="text-muted">-</span>
                </div>
              </div>

              <div class="info-item">
                <span class="label">技术负责人:</span>
                <div class="value">
                  <div v-if="product.managers && product.managers.tech_manager" class="manager-list">
                    <el-tag 
                      v-for="manager in (Array.isArray(product.managers.tech_manager) ? product.managers.tech_manager : [product.managers.tech_manager])" 
                      :key="manager" 
                      size="small" 
                      class="manager-tag"
                      type="warning"
                    >
                      {{ getUserDisplayName(manager) }}
                    </el-tag>
                  </div>
                  <span v-else class="text-muted">-</span>
                </div>
              </div>

              <div class="info-item">
                <span class="label">创建时间:</span>
                <span class="value">{{ formatDateTime(product.created_at) }}</span>
              </div>
            </div>

            <template #footer>
              <div class="card-footer">
                <el-button type="primary" size="small" @click="handleView(product)" :icon="View">
                  查看
                </el-button>
                <el-button type="warning" size="small" @click="handleEdit(product)" :icon="Edit">
                  编辑
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(product)" :icon="Delete">
                  删除
                </el-button>
              </div>
            </template>
          </el-card>
        </div>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 表单弹窗 -->
    <el-dialog
      v-model="formDialog.visible"
      :title="formDialog.title"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        label-width="100px"
        class="product-form"
      >
        <el-form-item label="产品代码" prop="product_code">
          <el-input 
            v-model="form.product_code" 
            placeholder="请输入产品代码"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="产品名称" prop="name">
          <el-input 
            v-model="form.name" 
            placeholder="请输入产品名称"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>
        
        
        <el-form-item label="所属区域" prop="region_id">
          <el-select 
            v-model="form.region_id" 
            placeholder="请选择区域"
            style="width: 100%"
          >
            <el-option 
              v-for="region in regionOptions" 
              :key="region.id" 
              :label="region.name || region.region_code" 
              :value="region.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="名称前缀" prop="name_prefix">
          <el-input 
            v-model="form.name_prefix" 
            placeholder="请输入名称前缀（可选）"
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="产品负责人" prop="manager">
          <el-select 
            v-model="form.manager" 
            placeholder="请选择产品负责人"
            style="width: 100%"
            filterable
            remote
            reserve-keyword
            :remote-method="searchUsers"
            :loading="userSearchLoading"
            clearable
          >
            <el-option 
              v-for="user in userOptions" 
              :key="user.id" 
              :label="`${user.username} (${user.first_name || user.username})`" 
              :value="user.id" 
            />
          </el-select>
          <div class="user-search-actions">
            <el-button 
              size="small" 
              type="text" 
              @click="loadMoreUsers"
              :loading="loadMoreLoading"
              v-if="hasMoreUsers"
            >
              加载更多用户
            </el-button>
          </div>
        </el-form-item>
        
        <el-form-item label="技术负责人" prop="tech_manager">
          <el-select 
            v-model="form.tech_manager" 
            placeholder="请选择技术负责人"
            style="width: 100%"
            filterable
            remote
            reserve-keyword
            :remote-method="searchUsers"
            :loading="userSearchLoading"
            clearable
          >
            <el-option 
              v-for="user in userOptions" 
              :key="user.id" 
              :label="`${user.username} (${user.first_name || user.username})`" 
              :value="user.id" 
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="描述" prop="description">
          <el-input 
            v-model="form.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入产品描述（可选）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="formDialog.visible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="handleFormSubmit"
            :loading="formDialog.loading"
          >
            {{ formDialog.mode === 'create' ? '创建' : '更新' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="产品详情"
      width="600px"
    >
      <div v-if="detailDialog.data" class="product-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="产品ID">
            {{ detailDialog.data.id }}
          </el-descriptions-item>
                    <el-descriptions-item label="产品代码">
            {{ detailDialog.data.product_code || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="产品名称">
            {{ detailDialog.data.name }}
          </el-descriptions-item>
          <el-descriptions-item label="所属区域">
            {{ detailDialog.data.region?.name || detailDialog.data.region?.region_code || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="名称前缀">
            {{ detailDialog.data.name_prefix || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="产品负责人">
            <div v-if="detailDialog.data.managers && detailDialog.data.managers.manager">
              <el-tag 
                v-for="manager in (Array.isArray(detailDialog.data.managers.manager) ? detailDialog.data.managers.manager : [detailDialog.data.managers.manager])" 
                :key="manager"
                size="small"
                class="manager-tag"
              >
                {{ getUserDisplayName(manager) }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="技术负责人">
            <div v-if="detailDialog.data.managers && detailDialog.data.managers.tech_manager">
              <el-tag 
                v-for="manager in (Array.isArray(detailDialog.data.managers.tech_manager) ? detailDialog.data.managers.tech_manager : [detailDialog.data.managers.tech_manager])" 
                :key="manager"
                size="small"
                class="manager-tag"
                type="warning"
              >
                {{ getUserDisplayName(manager) }}
              </el-tag>
            </div>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(detailDialog.data.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间" :span="2">
            {{ formatDateTime(detailDialog.data.updated_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="描述" :span="2">
            {{ detailDialog.data.description || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialog.visible = false">关闭</el-button>
          <el-button 
            type="primary" 
            @click="detailDialog.data && handleEdit(detailDialog.data)"
          >
            编辑
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance } from 'element-plus'
import { 
  Plus, Search, Refresh, Download, View, Edit, Delete, MoreFilled 
} from '@element-plus/icons-vue'
import { productApi, regionApi } from '../../../api/modules/cmdb'
import type { Product, Region, ProductCreateRequest } from '../../../api/modules/cmdb'
import { handleAPIResponse } from '../../../utils/response'
import userApi from '../../../api/modules/user'
import type { User as UserType } from '../../../api/modules/user'


interface SearchForm {
  name?: string
  region_id?: number
}

interface ProductForm {
  id?: number
  name: string
  product_code?: string
  region_id?: number
  name_prefix?: string
  manager?: number
  tech_manager?: number
  description?: string
}

interface Pagination {
  page: number
  pageSize: number
  total: number
}

// 响应式数据
const loading = ref(false)
const tableData = ref<Product[]>([])
const regionOptions = ref<Region[]>([])
const userOptions = ref<UserType[]>([])
const userSearchLoading = ref(false)
const loadMoreLoading = ref(false)
const hasMoreUsers = ref(true)
const currentUserPage = ref(1)
const userCache = ref<Map<number, UserType>>(new Map())

const searchForm = reactive<SearchForm>({
  name: '',
  region_id: undefined
})

const pagination = reactive<Pagination>({
  page: 1,
  pageSize: 20,
  total: 0
})

const formDialog = reactive({
  visible: false,
  title: '',
  mode: 'create' as 'create' | 'edit',
  loading: false
})

const detailDialog = reactive<{
  visible: boolean
  data: Product | null
}>({
  visible: false,
  data: null
})

const form = reactive<ProductForm>({
  name: '',
  product_code: '',
  region_id: undefined,
  name_prefix: '',
  manager: undefined,
  tech_manager: undefined,
  description: ''
})

// 表单验证规则
const formRules = {
  product_code: [
    { required: true, message: '请输入产品代码', trigger: 'blur' },
    { min: 2, max: 100, message: '产品代码长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入产品名称', trigger: 'blur' },
    { min: 2, max: 100, message: '产品名称长度在 2 到 100 个字符', trigger: 'blur' }
  ]
}

// 引用
const searchFormRef = ref<FormInstance>()
const tableRef = ref()
const formRef = ref<FormInstance>()



// 工具函数
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 获取用户显示名称
const getUserDisplayName = (user: any) => {
  if (!user) return '-'
  return user.display_name || user.username || `用户${user.id}`
}

// 搜索用户
const searchUsers = async (query: string) => {
  if (!query) {
    // 如果没有查询条件，加载初始用户列表
    await loadInitialUsers()
    return
  }
  
  userSearchLoading.value = true
  try {
    const response = await userApi.getUsers({ 
      page: 1,
      page_size: 20,
      username: query
    })
    const result = handleAPIResponse(response) as { items: UserType[] }
    userOptions.value = result.items || []
    
    // 更新用户缓存
    result.items.forEach(user => {
      userCache.value.set(user.id, user)
    })
  } catch (error) {
    console.error('搜索用户失败:', error)
    ElMessage.error('搜索用户失败')
  } finally {
    userSearchLoading.value = false
  }
}

// 加载初始用户列表
const loadInitialUsers = async () => {
  userSearchLoading.value = true
  try {
    const response = await userApi.getUsers({ 
      page: 1,
      page_size: 20
    })
    const result = handleAPIResponse(response) as { items: UserType[], total: number }
    userOptions.value = result.items || []
    hasMoreUsers.value = (result.items?.length || 0) < (result.total || 0)
    currentUserPage.value = 1
    
    // 更新用户缓存
    result.items.forEach(user => {
      userCache.value.set(user.id, user)
    })
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    userSearchLoading.value = false
  }
}

// 加载更多用户
const loadMoreUsers = async () => {
  loadMoreLoading.value = true
  try {
    const nextPage = currentUserPage.value + 1
    const response = await userApi.getUsers({ 
      page: nextPage,
      page_size: 20
    })
    const result = handleAPIResponse(response) as { items: UserType[], total: number }
    
    if (result.items && result.items.length > 0) {
      userOptions.value = [...userOptions.value, ...result.items]
      currentUserPage.value = nextPage
      
      // 更新用户缓存
      result.items.forEach(user => {
        userCache.value.set(user.id, user)
      })
      
      // 检查是否还有更多数据
      hasMoreUsers.value = userOptions.value.length < (result.total || 0)
    } else {
      hasMoreUsers.value = false
    }
  } catch (error) {
    console.error('加载更多用户失败:', error)
    ElMessage.error('加载更多用户失败')
  } finally {
    loadMoreLoading.value = false
  }
}

// 方法
const loadRegions = async () => {
  try {
    const response = await regionApi.getRegions({ page_size: 1000 })
    const result = handleAPIResponse(response) as { items: Region[] }
    regionOptions.value = result.items || []
  } catch (error) {
    console.error('加载区域列表失败:', error)
    ElMessage.error('加载区域列表失败')
  }
}

const loadData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      name: searchForm.name || undefined,
      region_id: searchForm.region_id || undefined
    }
    
    const response = await productApi.getProducts(params)
    const result = handleAPIResponse(response) as { 
      items: Product[]
      total: number 
      page: number
      page_size: number
    }
    
    tableData.value = result.items || []
    pagination.total = result.total || 0
    pagination.page = result.page || 1
    pagination.pageSize = result.page_size || 20
    
    // 收集所有用户ID并缓存用户信息
    const userIds = new Set<number>()
    result.items.forEach(product => {
      if (product.managers) {
        if (product.managers.manager) {
          const managers = Array.isArray(product.managers.manager) 
            ? product.managers.manager 
            : [product.managers.manager]
          managers.forEach(id => {
            if (typeof id === 'number') userIds.add(id)
            else if (typeof id === 'string') userIds.add(parseInt(id))
          })
        }
        if (product.managers.tech_manager) {
          const techManagers = Array.isArray(product.managers.tech_manager) 
            ? product.managers.tech_manager 
            : [product.managers.tech_manager]
          techManagers.forEach(id => {
            if (typeof id === 'number') userIds.add(id)
            else if (typeof id === 'string') userIds.add(parseInt(id))
          })
        }
      }
    })
    
    // 批量获取用户信息
    if (userIds.size > 0) {
      await loadUsersByIds(Array.from(userIds))
    }
  } catch (error) {
    console.error('加载产品列表失败:', error)
    ElMessage.error('加载产品列表失败')
  } finally {
    loading.value = false
  }
}

// 根据ID批量加载用户信息
const loadUsersByIds = async (userIds: number[]) => {
  try {
    // 过滤掉已缓存的用户
    const uncachedIds = userIds.filter(id => !userCache.value.has(id))
    
    if (uncachedIds.length === 0) return
    
    // 分批加载用户（每次最多50个）
    const batchSize = 50
    for (let i = 0; i < uncachedIds.length; i += batchSize) {
      const batch = uncachedIds.slice(i, i + batchSize)
             const response = await userApi.getUsers({ 
         page: 1,
         page_size: batchSize,
         // 这里假设API支持通过ID列表查询，如果不支持需要调整
       })
       const result = handleAPIResponse(response) as { items: UserType[] }
      
      result.items.forEach(user => {
        userCache.value.set(user.id, user)
      })
    }
  } catch (error) {
    console.error('批量加载用户信息失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadData()
}

const handleReset = () => {
  searchFormRef.value?.resetFields()
  Object.assign(searchForm, {
    name: '',
    region_id: undefined
  })
  pagination.page = 1
  loadData()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadData()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadData()
}

const handleSortChange = ({ prop, order }: any) => {
  // 这里可以实现排序逻辑
  console.log('排序:', prop, order)
}

const handleCreate = () => {
  formDialog.visible = true
  formDialog.title = '新建产品'
  formDialog.mode = 'create'
  resetForm()
  loadInitialUsers()
}

const handleEdit = (product: Product) => {
  formDialog.visible = true
  formDialog.title = '编辑产品'
  formDialog.mode = 'edit'
  
  // 提取负责人ID
  let managerId: number | undefined = undefined
  let techManagerId: number | undefined = undefined
  
  if (product.managers) {
    if (product.managers.manager) {
      const managers = Array.isArray(product.managers.manager) 
        ? product.managers.manager 
        : [product.managers.manager]
      managerId = typeof managers[0] === 'number' ? managers[0] : parseInt(managers[0])
    }
    
    if (product.managers.tech_manager) {
      const techManagers = Array.isArray(product.managers.tech_manager) 
        ? product.managers.tech_manager 
        : [product.managers.tech_manager]
      techManagerId = typeof techManagers[0] === 'number' ? techManagers[0] : parseInt(techManagers[0])
    }
  }
  
  Object.assign(form, {
    id: product.id,
    name: product.name,
    product_code: product.product_code || '',
    region_id: product.region_id,
    name_prefix: product.name_prefix || '',
    manager: managerId,
    tech_manager: techManagerId,
    description: product.description || ''
  })
  
  loadInitialUsers()
  detailDialog.visible = false
}

const handleView = (product: Product) => {
  detailDialog.visible = true
  detailDialog.data = product
}

const handleDelete = async (product: Product) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除产品 "${product.name}" 吗？此操作不可恢复！`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await productApi.deleteProduct(product.id)
    ElMessage.success('删除成功')
    loadData()
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除产品失败:', error)
      ElMessage.error('删除产品失败')
    }
  }
}

const resetForm = () => {
  Object.assign(form, {
    id: undefined,
    name: '',
    product_code: '',
    region_id: undefined,
    name_prefix: '',
    manager: undefined,
    tech_manager: undefined,
    description: ''
  })
  formRef.value?.clearValidate()
}

const handleFormSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    formDialog.loading = true
    
    const submitData: ProductCreateRequest = {
      name: form.name,
      product_code: form.product_code || undefined,
      region_id: form.region_id || undefined,
      name_prefix: form.name_prefix || undefined,
      managers: {
        manager: form.manager || undefined,
        tech_manager: form.tech_manager || undefined
      },
      description: form.description || undefined
    }
    
    if (formDialog.mode === 'create') {
      await productApi.createProduct(submitData)
      ElMessage.success('创建成功')
    } else {
      await productApi.updateProduct(form.id!, submitData)
      ElMessage.success('更新成功')
    }
    
    formDialog.visible = false
    loadData()
  } catch (error) {
    console.error('提交表单失败:', error)
    ElMessage.error('操作失败')
  } finally {
    formDialog.loading = false
  }
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 生命周期
onMounted(() => {
  loadRegions()
  loadData()
})
</script>

<style scoped>
.product-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h3 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form-wrapper {
  padding: 10px 0;
}

.search-form .el-form-item {
  margin-bottom: 0;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-title .ml-2 {
  margin-left: 8px;
}

.product-name {
  display: flex;
  flex-direction: column;
}

.product-name .name {
  font-weight: 600;
  color: #303133;
}

.product-name .display-name {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.manager-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.manager-tag {
  margin: 2px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.product-form .el-form-item {
  margin-bottom: 20px;
}

.user-search-actions {
  margin-top: 8px;
  text-align: right;
}

.product-detail .manager-tag {
  margin: 2px 4px 2px 0;
}

.text-muted {
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

/* PC端表格显示，移动端隐藏 */
.desktop-table {
  display: block;
}

.mobile-cards {
  display: none;
}

/* 移动端卡片样式 */
.product-card {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.card-title .product-id {
  font-size: 12px;
  color: #909399;
}

.card-title .product-name {
  font-weight: 500;
  font-size: 16px;
}

.card-title .product-code {
  font-size: 12px;
  color: #909399;
}

.more-btn {
  padding: 4px;
}

.card-content {
  padding: 8px 0;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.info-item .label {
  min-width: 100px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
}

.info-item .value {
  margin-left: 12px;
  flex: 1;
}

.card-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px;
}

/* 移动端样式 */
@media (max-width: 768px) {
  .product-page {
    padding: 10px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-right {
    width: 100%;
  }
  
  .search-form {
    flex-direction: column;
  }
  
  .search-form .el-form-item {
    margin-bottom: 12px;
    width: 100%;
  }
  
  /* 移动端隐藏表格，显示卡片 */
  .desktop-table {
    display: none;
  }
  
  .mobile-cards {
    display: block;
  }
  
  .card-footer .el-button {
    flex: 1;
  }
  
  .pagination-wrapper .el-pagination {
    justify-content: center;
  }
  
  .pagination-wrapper .el-pagination .el-pagination__sizes,
  .pagination-wrapper .el-pagination .el-pagination__jump {
    display: none;
  }
}

/* 平板端适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .product-page {
    padding: 15px;
  }
}
</style> 