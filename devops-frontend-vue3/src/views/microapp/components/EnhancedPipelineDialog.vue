<template>
  <el-dialog
    :model-value="visible"
    title="流水线管理"
    width="90%"
    top="5vh"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-tabs v-model="activeTab" class="pipeline-tabs">
      <!-- 流水线配置 -->
      <el-tab-pane label="流水线配置" name="config">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-form :model="pipelineForm" :rules="rules" ref="formRef" label-width="120px">
              <el-form-item label="流水线名称" prop="name">
                <el-input v-model="pipelineForm.name" placeholder="请输入流水线名称" />
              </el-form-item>

              <el-form-item label="触发方式" prop="trigger">
                <el-checkbox-group v-model="pipelineForm.triggers">
                  <el-checkbox value="push">代码推送</el-checkbox>
                  <el-checkbox value="pr">Pull Request</el-checkbox>
                  <el-checkbox value="manual">手动触发</el-checkbox>
                  <el-checkbox value="schedule">定时触发</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item label="分支过滤" prop="branches">
                <el-select 
                  v-model="pipelineForm.branches" 
                  multiple 
                  placeholder="选择分支，留空表示所有分支"
                  style="width: 100%"
                >
                  <el-option label="main" value="main" />
                  <el-option label="develop" value="develop" />
                  <el-option label="release/*" value="release/*" />
                  <el-option label="feature/*" value="feature/*" />
                </el-select>
              </el-form-item>

              <el-form-item label="环境变量">
                <div class="env-variables">
                  <el-table :data="pipelineForm.envVars" size="small" max-height="200">
                    <el-table-column prop="key" label="变量名" width="150">
                      <template #default="{ row }">
                        <el-input v-model="row.key" size="small" placeholder="变量名" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="value" label="变量值">
                      <template #default="{ row }">
                        <el-input v-model="row.value" size="small" placeholder="变量值" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="secret" label="加密" width="80">
                      <template #default="{ row }">
                        <el-switch v-model="row.secret" size="small" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                      <template #default="{ $index }">
                        <el-button size="small" type="danger" @click="removeEnvVar($index)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button size="small" type="primary" @click="addEnvVar" style="margin-top: 10px;">
                    添加环境变量
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item label="通知设置">
                <el-checkbox-group v-model="pipelineForm.notifications">
                  <el-checkbox value="success">成功时通知</el-checkbox>
                  <el-checkbox value="failure">失败时通知</el-checkbox>
                  <el-checkbox value="always">总是通知</el-checkbox>
                </el-checkbox-group>
              </el-form-item>

              <el-form-item label="通知方式">
                <el-checkbox-group v-model="pipelineForm.notificationMethods">
                  <el-checkbox value="email">邮件</el-checkbox>
                  <el-checkbox value="webhook">Webhook</el-checkbox>
                  <el-checkbox value="dingtalk">钉钉</el-checkbox>
                  <el-checkbox value="wechat">企业微信</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </el-col>
          
          <el-col :span="8">
            <!-- 流水线预览 -->
            <el-card class="pipeline-preview" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>流水线预览</span>
                </div>
              </template>
              <div class="pipeline-stages">
                <div v-for="(stage, index) in pipelineStages" :key="stage.name" class="stage-item">
                  <div class="stage-icon">
                    <el-icon :class="getStageIconClass(stage.status)">
                      <Loading v-if="stage.status === 'running'" />
                      <CircleCheck v-else-if="stage.status === 'success'" />
                      <CircleClose v-else-if="stage.status === 'failed'" />
                      <Clock v-else />
                    </el-icon>
                  </div>
                  <div class="stage-content">
                    <div class="stage-name">{{ stage.name }}</div>
                    <div class="stage-description">{{ stage.description }}</div>
                    <div class="stage-duration" v-if="stage.duration">{{ stage.duration }}</div>
                  </div>
                  <div v-if="index < pipelineStages.length - 1" class="stage-connector"></div>
                </div>
              </div>
            </el-card>

            <!-- 快速模板 -->
            <el-card class="pipeline-templates" shadow="hover" style="margin-top: 20px">
              <template #header>
                <div class="card-header">
                  <span>快速模板</span>
                </div>
              </template>
              <div class="template-list">
                <div 
                  v-for="template in pipelineTemplates" 
                  :key="template.name" 
                  class="template-item"
                  @click="applyTemplate(template)"
                >
                  <div class="template-icon">
                    <el-icon><Document /></el-icon>
                  </div>
                  <div class="template-info">
                    <div class="template-name">{{ template.name }}</div>
                    <div class="template-description">{{ template.description }}</div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <!-- 阶段配置 -->
      <el-tab-pane label="阶段配置" name="stages">
        <div class="stages-config">
          <div class="stages-toolbar">
            <el-button type="primary" @click="addStage">
              <el-icon><Plus /></el-icon>
              添加阶段
            </el-button>
            <el-button @click="resetStages">重置</el-button>
          </div>

          <div class="stages-list">
            <!-- 暂时使用普通列表，后续可添加拖拽功能 -->
            <div class="draggable-list">
              <div v-for="(stage, index) in pipelineStages" :key="stage.id">
                <el-card class="stage-config-card" shadow="hover">
                  <template #header>
                    <div class="stage-header">
                      <div class="stage-title">
                        <el-icon><Rank /></el-icon>
                        <span>阶段 {{ index + 1 }}: {{ stage.name }}</span>
                      </div>
                      <div class="stage-actions">
                        <el-button size="small" @click="editStage(stage, index)">编辑</el-button>
                        <el-button size="small" type="danger" @click="removeStage(index)">删除</el-button>
                      </div>
                    </div>
                  </template>
                  
                  <div class="stage-content">
                    <div class="stage-info">
                      <div class="info-item">
                        <span class="label">类型:</span>
                        <el-tag size="small">{{ getStageTypeLabel(stage.type) }}</el-tag>
                      </div>
                      <div class="info-item">
                        <span class="label">超时:</span>
                        <span>{{ stage.timeout || '30' }} 分钟</span>
                      </div>
                      <div class="info-item">
                        <span class="label">失败策略:</span>
                        <span>{{ getFailureStrategyLabel(stage.failureStrategy) }}</span>
                      </div>
                    </div>
                    
                    <div class="stage-steps">
                      <div class="steps-title">执行步骤:</div>
                      <div v-for="(step, stepIndex) in stage.steps" :key="stepIndex" class="step-item">
                        <el-icon><Right /></el-icon>
                        <span>{{ step.name }}</span>
                      </div>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 执行历史 -->
      <el-tab-pane label="执行历史" name="history">
        <div class="pipeline-history">
          <div class="history-filters">
            <el-form inline>
              <el-form-item label="状态">
                <el-select v-model="historyFilter.status" placeholder="全部状态" clearable>
                  <el-option label="成功" value="success" />
                  <el-option label="失败" value="failed" />
                  <el-option label="进行中" value="running" />
                  <el-option label="已取消" value="cancelled" />
                </el-select>
              </el-form-item>
              <el-form-item label="分支">
                <el-select v-model="historyFilter.branch" placeholder="全部分支" clearable>
                  <el-option label="main" value="main" />
                  <el-option label="develop" value="develop" />
                  <el-option label="feature/new-ui" value="feature/new-ui" />
                </el-select>
              </el-form-item>
              <el-form-item label="触发方式">
                <el-select v-model="historyFilter.trigger" placeholder="全部方式" clearable>
                  <el-option label="代码推送" value="push" />
                  <el-option label="手动触发" value="manual" />
                  <el-option label="定时触发" value="schedule" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="loadPipelineHistory">查询</el-button>
                <el-button @click="resetHistoryFilter">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table :data="pipelineHistory" v-loading="historyLoading">
            <el-table-column prop="id" label="执行ID" width="120" />
            <el-table-column prop="branch" label="分支" width="120" />
            <el-table-column prop="commit" label="提交" width="100">
              <template #default="{ row }">
                <el-link type="primary" :href="row.commitUrl" target="_blank">
                  {{ row.commit.substring(0, 8) }}
                </el-link>
              </template>
            </el-table-column>
            <el-table-column prop="trigger" label="触发方式" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getTriggerTagType(row.trigger)">
                  {{ getTriggerLabel(row.trigger) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag size="small" :type="getStatusTagType(row.status)">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="耗时" width="100" />
            <el-table-column prop="triggered_by" label="触发人" width="100" />
            <el-table-column prop="created_at" label="开始时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="viewPipelineDetail(row)">详情</el-button>
                <el-button size="small" @click="viewPipelineLogs(row)">日志</el-button>
                <el-button 
                  size="small" 
                  type="primary" 
                  @click="rerunPipeline(row)"
                  :disabled="row.status === 'running'"
                >
                  重新执行
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="cancelPipeline(row)"
                  :disabled="row.status !== 'running'"
                >
                  取消
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              :current-page="historyPagination.page"
              :page-size="historyPagination.pageSize"
              :total="historyPagination.total"
              layout="total, prev, pager, next"
              @current-change="handleHistoryPageChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 实时监控 -->
      <el-tab-pane label="实时监控" name="monitor">
        <div class="pipeline-monitor">
          <el-row :gutter="20">
            <el-col :span="16">
              <!-- 流水线进度 -->
              <el-card class="monitor-card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>流水线执行进度</span>
                    <div class="header-actions">
                      <el-tag :type="getStatusTagType(currentExecution.status)">
                        {{ getStatusLabel(currentExecution.status) }}
                      </el-tag>
                      <el-button size="small" @click="refreshExecution">刷新</el-button>
                    </div>
                  </div>
                </template>
                
                <div class="execution-progress">
                  <div class="progress-info">
                    <div class="info-item">
                      <span class="label">执行ID:</span>
                      <span>{{ currentExecution.id }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">分支:</span>
                      <span>{{ currentExecution.branch }}</span>
                    </div>
                    <div class="info-item">
                      <span class="label">提交:</span>
                      <el-link type="primary">{{ currentExecution.commit }}</el-link>
                    </div>
                    <div class="info-item">
                      <span class="label">已耗时:</span>
                      <span>{{ currentExecution.duration }}</span>
                    </div>
                  </div>
                  
                  <div class="stages-progress">
                    <div v-for="stage in currentExecution.stages" :key="stage.name" class="stage-progress">
                      <div class="stage-header">
                        <div class="stage-info">
                          <el-icon :class="getStageIconClass(stage.status)">
                            <Loading v-if="stage.status === 'running'" />
                            <CircleCheck v-else-if="stage.status === 'success'" />
                            <CircleClose v-else-if="stage.status === 'failed'" />
                            <Clock v-else />
                          </el-icon>
                          <span class="stage-name">{{ stage.name }}</span>
                        </div>
                        <div class="stage-duration">{{ stage.duration || '-' }}</div>
                      </div>
                      
                      <div class="stage-steps">
                        <div v-for="step in stage.steps" :key="step.name" class="step-progress">
                          <el-icon :class="getStepIconClass(step.status)">
                            <Loading v-if="step.status === 'running'" />
                            <CircleCheck v-else-if="step.status === 'success'" />
                            <CircleClose v-else-if="step.status === 'failed'" />
                            <Clock v-else />
                          </el-icon>
                          <span class="step-name">{{ step.name }}</span>
                          <span class="step-duration">{{ step.duration || '-' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="8">
              <!-- 统计信息 -->
              <el-card class="stats-card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>统计信息</span>
                  </div>
                </template>
                <div class="stats-content">
                  <div class="stat-item">
                    <div class="stat-value">{{ pipelineStats.totalRuns }}</div>
                    <div class="stat-label">总执行次数</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value success">{{ pipelineStats.successRate }}%</div>
                    <div class="stat-label">成功率</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ pipelineStats.avgDuration }}</div>
                    <div class="stat-label">平均耗时</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-value">{{ pipelineStats.lastRun }}</div>
                    <div class="stat-label">最后执行</div>
                  </div>
                </div>
              </el-card>

              <!-- 快速操作 -->
              <el-card class="quick-actions" shadow="hover" style="margin-top: 20px">
                <template #header>
                  <div class="card-header">
                    <span>快速操作</span>
                  </div>
                </template>
                <div class="actions-content">
                  <el-button type="primary" @click="triggerPipeline" :loading="triggerLoading">
                    <el-icon><VideoPlay /></el-icon>
                    手动触发
                  </el-button>
                  <el-button @click="savePipelineConfig" :loading="saveLoading">
                    <el-icon><Document /></el-icon>
                    保存配置
                  </el-button>
                  <el-button @click="exportPipelineConfig">
                    <el-icon><Download /></el-icon>
                    导出配置
                  </el-button>
                  <el-button @click="importPipelineConfig">
                    <el-icon><Upload /></el-icon>
                    导入配置
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 实时日志 -->
          <el-card class="log-card" shadow="hover" style="margin-top: 20px">
            <template #header>
              <div class="card-header">
                <span>实时日志</span>
                <div class="log-actions">
                  <el-button size="small" @click="refreshLogs">刷新</el-button>
                  <el-button size="small" @click="downloadLogs">下载</el-button>
                  <el-button size="small" @click="clearLogs">清空</el-button>
                  <el-switch v-model="autoRefreshLogs" active-text="自动刷新" />
                </div>
              </div>
            </template>
            <div class="log-content">
              <div class="log-viewer" ref="logViewer">
                <div v-for="(log, index) in pipelineLogs" :key="index" class="log-line">
                  <span class="log-time">{{ log.timestamp }}</span>
                  <span class="log-stage">[{{ log.stage }}]</span>
                  <span :class="['log-level', `log-${log.level}`]">{{ log.level }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button 
          v-if="activeTab === 'config'"
          type="primary" 
          :loading="saveLoading" 
          @click="savePipelineConfig"
        >
          保存配置
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Plus, Document, Rank, Right, Clock, Loading, CircleCheck, CircleClose,
  VideoPlay, Download, Upload
} from '@element-plus/icons-vue'
// import draggable from 'vuedraggable' // 需要安装依赖

interface Props {
  visible: boolean
  appData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()
const logViewer = ref<HTMLElement>()

// 状态管理
const activeTab = ref('config')
const saveLoading = ref(false)
const triggerLoading = ref(false)
const historyLoading = ref(false)
const autoRefreshLogs = ref(false)
let logRefreshTimer: NodeJS.Timeout | null = null

// 流水线表单
const pipelineForm = reactive({
  name: '',
  triggers: ['push'],
  branches: [],
  envVars: [
    { key: '', value: '', secret: false }
  ],
  notifications: ['failure'],
  notificationMethods: ['email']
})

// 表单验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入流水线名称', trigger: 'blur' }
  ]
}

// 流水线阶段
const pipelineStages = ref([
  {
    id: 1,
    name: '代码检出',
    type: 'checkout',
    status: 'success',
    description: '从Git仓库检出代码',
    duration: '30s',
    timeout: 5,
    failureStrategy: 'fail',
    steps: [
      { name: 'Git Clone', status: 'success', duration: '15s' },
      { name: '切换分支', status: 'success', duration: '5s' },
      { name: '下载依赖', status: 'success', duration: '10s' }
    ]
  },
  {
    id: 2,
    name: '代码构建',
    type: 'build',
    status: 'running',
    description: '编译和构建应用',
    duration: '2m 15s',
    timeout: 30,
    failureStrategy: 'fail',
    steps: [
      { name: '安装依赖', status: 'success', duration: '45s' },
      { name: '代码编译', status: 'running', duration: '-' },
      { name: '单元测试', status: 'pending', duration: '-' }
    ]
  },
  {
    id: 3,
    name: '镜像构建',
    type: 'docker',
    status: 'pending',
    description: '构建Docker镜像',
    duration: '-',
    timeout: 20,
    failureStrategy: 'fail',
    steps: [
      { name: '构建镜像', status: 'pending', duration: '-' },
      { name: '推送镜像', status: 'pending', duration: '-' }
    ]
  },
  {
    id: 4,
    name: '部署应用',
    type: 'deploy',
    status: 'pending',
    description: '部署到目标环境',
    duration: '-',
    timeout: 15,
    failureStrategy: 'fail',
    steps: [
      { name: '更新配置', status: 'pending', duration: '-' },
      { name: '滚动更新', status: 'pending', duration: '-' },
      { name: '健康检查', status: 'pending', duration: '-' }
    ]
  }
])

// 流水线模板
const pipelineTemplates = ref([
  {
    name: 'Node.js应用',
    description: '适用于Node.js前端和后端应用',
    stages: ['checkout', 'build', 'test', 'docker', 'deploy']
  },
  {
    name: 'Java应用',
    description: '适用于Spring Boot等Java应用',
    stages: ['checkout', 'build', 'test', 'sonar', 'docker', 'deploy']
  },
  {
    name: 'Python应用',
    description: '适用于Django、Flask等Python应用',
    stages: ['checkout', 'build', 'test', 'lint', 'docker', 'deploy']
  },
  {
    name: '静态网站',
    description: '适用于Vue、React等静态网站',
    stages: ['checkout', 'build', 'test', 'deploy-static']
  }
])

// 执行历史
const historyFilter = reactive({
  status: '',
  branch: '',
  trigger: ''
})

const historyPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

const pipelineHistory = ref([
  {
    id: 'pipeline-001',
    branch: 'main',
    commit: 'abc123def456',
    commitUrl: 'https://github.com/example/repo/commit/abc123def456',
    trigger: 'push',
    status: 'success',
    duration: '8m 32s',
    triggered_by: 'developer',
    created_at: '2024-01-15 14:30:00'
  },
  {
    id: 'pipeline-002',
    branch: 'develop',
    commit: 'def456ghi789',
    commitUrl: 'https://github.com/example/repo/commit/def456ghi789',
    trigger: 'manual',
    status: 'failed',
    duration: '3m 15s',
    triggered_by: 'admin',
    created_at: '2024-01-15 10:15:00'
  }
])

// 当前执行状态
const currentExecution = reactive({
  id: 'pipeline-003',
  branch: 'feature/new-ui',
  commit: 'ghi789jkl012',
  status: 'running',
  duration: '5m 23s',
  stages: [
    {
      name: '代码检出',
      status: 'success',
      duration: '30s',
      steps: [
        { name: 'Git Clone', status: 'success', duration: '15s' },
        { name: '切换分支', status: 'success', duration: '5s' },
        { name: '下载依赖', status: 'success', duration: '10s' }
      ]
    },
    {
      name: '代码构建',
      status: 'running',
      duration: '2m 15s',
      steps: [
        { name: '安装依赖', status: 'success', duration: '45s' },
        { name: '代码编译', status: 'running', duration: '-' },
        { name: '单元测试', status: 'pending', duration: '-' }
      ]
    }
  ]
})

// 统计信息
const pipelineStats = reactive({
  totalRuns: 156,
  successRate: 87,
  avgDuration: '6m 45s',
  lastRun: '2小时前'
})

// 流水线日志
const pipelineLogs = ref([
  { timestamp: '14:30:01', stage: 'checkout', level: 'info', message: '开始检出代码...' },
  { timestamp: '14:30:05', stage: 'checkout', level: 'info', message: '克隆仓库: https://github.com/example/repo.git' },
  { timestamp: '14:30:15', stage: 'checkout', level: 'info', message: '切换到分支: feature/new-ui' },
  { timestamp: '14:30:20', stage: 'build', level: 'info', message: '开始构建应用...' },
  { timestamp: '14:30:25', stage: 'build', level: 'info', message: '安装依赖包...' },
  { timestamp: '14:31:10', stage: 'build', level: 'info', message: '编译TypeScript代码...' },
  { timestamp: '14:31:45', stage: 'build', level: 'warn', message: '发现1个警告，但构建继续' },
  { timestamp: '14:32:00', stage: 'build', level: 'info', message: '运行单元测试...' }
])

// 方法
const addEnvVar = () => {
  pipelineForm.envVars.push({ key: '', value: '', secret: false })
}

const removeEnvVar = (index: number) => {
  pipelineForm.envVars.splice(index, 1)
}

const getStageIconClass = (status: string) => {
  const classMap: Record<string, string> = {
    'running': 'stage-running',
    'success': 'stage-success',
    'failed': 'stage-failed',
    'pending': 'stage-pending'
  }
  return classMap[status] || 'stage-pending'
}

const getStepIconClass = (status: string) => {
  return getStageIconClass(status)
}

const getStageTypeLabel = (type: string) => {
  const labelMap: Record<string, string> = {
    'checkout': '代码检出',
    'build': '代码构建',
    'test': '测试',
    'docker': '镜像构建',
    'deploy': '部署',
    'sonar': '代码扫描'
  }
  return labelMap[type] || type
}

const getFailureStrategyLabel = (strategy: string) => {
  const labelMap: Record<string, string> = {
    'fail': '失败时停止',
    'continue': '失败时继续',
    'ignore': '忽略失败'
  }
  return labelMap[strategy] || strategy
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning',
    'cancelled': 'info',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'success': '成功',
    'failed': '失败',
    'running': '进行中',
    'cancelled': '已取消',
    'pending': '等待中'
  }
  return labelMap[status] || status
}

const getTriggerTagType = (trigger: string) => {
  const typeMap: Record<string, string> = {
    'push': 'primary',
    'manual': 'success',
    'schedule': 'warning',
    'pr': 'info'
  }
  return typeMap[trigger] || 'info'
}

const getTriggerLabel = (trigger: string) => {
  const labelMap: Record<string, string> = {
    'push': '代码推送',
    'manual': '手动触发',
    'schedule': '定时触发',
    'pr': 'Pull Request'
  }
  return labelMap[trigger] || trigger
}

// 应用模板
const applyTemplate = (template: any) => {
  ElMessageBox.confirm(
    `确定要应用模板 "${template.name}" 吗？这将覆盖当前配置。`,
    '确认应用模板',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 应用模板逻辑
    ElMessage.success(`已应用模板: ${template.name}`)
  }).catch(() => {})
}

// 阶段管理
const addStage = () => {
  const newStage = {
    id: Date.now(),
    name: `新阶段 ${pipelineStages.value.length + 1}`,
    type: 'build',
    status: 'pending',
    description: '新增的流水线阶段',
    duration: '-',
    timeout: 30,
    failureStrategy: 'fail',
    steps: [
      { name: '步骤1', status: 'pending', duration: '-' }
    ]
  }
  pipelineStages.value.push(newStage)
}

const editStage = (stage: any, index: number) => {
  ElMessage.info(`编辑阶段: ${stage.name}`)
}

const removeStage = (index: number) => {
  pipelineStages.value.splice(index, 1)
}

const resetStages = () => {
  ElMessageBox.confirm(
    '确定要重置所有阶段配置吗？',
    '确认重置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 重置为默认阶段
    ElMessage.success('阶段配置已重置')
  }).catch(() => {})
}

const onStageOrderChange = () => {
  ElMessage.success('阶段顺序已更新')
}

// 历史管理
const loadPipelineHistory = async () => {
  historyLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    console.error('加载流水线历史失败:', error)
  } finally {
    historyLoading.value = false
  }
}

const resetHistoryFilter = () => {
  historyFilter.status = ''
  historyFilter.branch = ''
  historyFilter.trigger = ''
  loadPipelineHistory()
}

const handleHistoryPageChange = (page: number) => {
  historyPagination.page = page
  loadPipelineHistory()
}

const viewPipelineDetail = (pipeline: any) => {
  ElMessage.info(`查看流水线详情: ${pipeline.id}`)
}

const viewPipelineLogs = (pipeline: any) => {
  ElMessage.info(`查看流水线日志: ${pipeline.id}`)
}

const rerunPipeline = async (pipeline: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要重新执行流水线 ${pipeline.id} 吗？`,
      '确认重新执行',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('流水线已重新执行')
  } catch (error) {
    // 用户取消
  }
}

const cancelPipeline = async (pipeline: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要取消流水线 ${pipeline.id} 吗？`,
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('流水线已取消')
  } catch (error) {
    // 用户取消
  }
}

// 监控相关
const refreshExecution = () => {
  ElMessage.success('执行状态已刷新')
}

const triggerPipeline = async () => {
  triggerLoading.value = true
  try {
    // 模拟触发流水线
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('流水线已触发')
    activeTab.value = 'monitor'
  } catch (error) {
    console.error('触发流水线失败:', error)
  } finally {
    triggerLoading.value = false
  }
}

const savePipelineConfig = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    saveLoading.value = true

    // 模拟保存配置
    await new Promise(resolve => setTimeout(resolve, 1000))

    ElMessage.success('流水线配置已保存')
    emit('success')
  } catch (error) {
    console.error('保存配置失败:', error)
  } finally {
    saveLoading.value = false
  }
}

const exportPipelineConfig = () => {
  const config = {
    pipeline: pipelineForm,
    stages: pipelineStages.value
  }

  const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `pipeline-config-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const importPipelineConfig = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const config = JSON.parse(e.target?.result as string)
          // 导入配置逻辑
          ElMessage.success('配置导入成功')
        } catch (error) {
          ElMessage.error('配置文件格式错误')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 日志相关
const refreshLogs = async () => {
  // 模拟刷新日志
  pipelineLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    stage: 'build',
    level: 'info',
    message: '日志已刷新'
  })

  // 滚动到底部
  await nextTick()
  if (logViewer.value) {
    logViewer.value.scrollTop = logViewer.value.scrollHeight
  }
}

const downloadLogs = () => {
  const logContent = pipelineLogs.value
    .map(log => `${log.timestamp} [${log.stage}] [${log.level.toUpperCase()}] ${log.message}`)
    .join('\n')

  const blob = new Blob([logContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `pipeline-logs-${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

const clearLogs = () => {
  pipelineLogs.value = []
  ElMessage.success('日志已清空')
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 监听自动刷新日志
watch(autoRefreshLogs, (newVal) => {
  if (newVal) {
    logRefreshTimer = setInterval(refreshLogs, 3000)
  } else {
    if (logRefreshTimer) {
      clearInterval(logRefreshTimer)
      logRefreshTimer = null
    }
  }
})

// 生命周期
onMounted(() => {
  loadPipelineHistory()
})

onUnmounted(() => {
  if (logRefreshTimer) {
    clearInterval(logRefreshTimer)
  }
})
</script>

<style scoped lang="scss">
.pipeline-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
}

.pipeline-preview {
  .pipeline-stages {
    .stage-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
      position: relative;

      .stage-icon {
        margin-right: 12px;
        margin-top: 2px;

        .el-icon {
          &.stage-running {
            color: #409eff;
          }

          &.stage-success {
            color: #67c23a;
          }

          &.stage-failed {
            color: #f56c6c;
          }

          &.stage-pending {
            color: #909399;
          }
        }
      }

      .stage-content {
        flex: 1;

        .stage-name {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .stage-description {
          color: #606266;
          font-size: 12px;
          margin-bottom: 4px;
        }

        .stage-duration {
          color: #909399;
          font-size: 12px;
        }
      }

      .stage-connector {
        position: absolute;
        left: 10px;
        top: 24px;
        bottom: -16px;
        width: 2px;
        background: #e4e7ed;
      }

      &:last-child .stage-connector {
        display: none;
      }
    }
  }
}

.pipeline-templates {
  .template-list {
    .template-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }

      .template-icon {
        margin-right: 12px;
        color: #409eff;
      }

      .template-info {
        flex: 1;

        .template-name {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .template-description {
          color: #606266;
          font-size: 12px;
        }
      }
    }
  }
}

.env-variables {
  .el-table {
    margin-bottom: 10px;
  }
}

.stages-config {
  .stages-toolbar {
    margin-bottom: 20px;
    display: flex;
    gap: 10px;
  }

  .stages-list {
    .draggable-list {
      .stage-config-card {
        margin-bottom: 16px;

        .stage-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .stage-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
          }

          .stage-actions {
            display: flex;
            gap: 8px;
          }
        }

        .stage-content {
          .stage-info {
            display: flex;
            gap: 20px;
            margin-bottom: 16px;

            .info-item {
              display: flex;
              align-items: center;
              gap: 8px;

              .label {
                color: #606266;
                font-size: 14px;
              }
            }
          }

          .stage-steps {
            .steps-title {
              font-weight: 500;
              margin-bottom: 8px;
              color: #303133;
            }

            .step-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;
              color: #606266;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

.pipeline-history {
  .history-filters {
    margin-bottom: 20px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}

.pipeline-monitor {
  .monitor-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-actions {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    .execution-progress {
      .progress-info {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;

        .info-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .label {
            color: #606266;
            font-size: 14px;
          }
        }
      }

      .stages-progress {
        .stage-progress {
          margin-bottom: 20px;

          .stage-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .stage-info {
              display: flex;
              align-items: center;
              gap: 8px;

              .stage-name {
                font-weight: 500;
              }
            }

            .stage-duration {
              color: #909399;
              font-size: 12px;
            }
          }

          .stage-steps {
            padding-left: 24px;

            .step-progress {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 4px;

              .step-name {
                flex: 1;
                color: #606266;
              }

              .step-duration {
                color: #909399;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  .stats-card {
    .stats-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 16px;

      .stat-item {
        text-align: center;

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 4px;

          &.success {
            color: #67c23a;
          }
        }

        .stat-label {
          color: #606266;
          font-size: 12px;
        }
      }
    }
  }

  .quick-actions {
    .actions-content {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .el-button {
        justify-content: flex-start;
      }
    }
  }

  .log-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .log-actions {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    .log-content {
      .log-viewer {
        height: 300px;
        overflow-y: auto;
        background: #1e1e1e;
        color: #d4d4d4;
        padding: 10px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;

        .log-line {
          margin-bottom: 2px;

          .log-time {
            color: #569cd6;
            margin-right: 8px;
          }

          .log-stage {
            color: #4ec9b0;
            margin-right: 8px;
          }

          .log-level {
            margin-right: 8px;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
            font-weight: bold;

            &.log-info {
              background: #4ec9b0;
              color: #1e1e1e;
            }

            &.log-warn {
              background: #dcdcaa;
              color: #1e1e1e;
            }

            &.log-error {
              background: #f44747;
              color: #ffffff;
            }

            &.log-debug {
              background: #9cdcfe;
              color: #1e1e1e;
            }
          }

          .log-message {
            color: #d4d4d4;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 响应式设计
@media (max-width: 768px) {
  .pipeline-tabs {
    :deep(.el-tab-pane) {
      padding: 0 10px;
    }
  }

  .pipeline-preview,
  .pipeline-templates {
    margin-top: 20px;
  }

  .pipeline-monitor {
    .el-row {
      .el-col {
        margin-bottom: 20px;
      }
    }

    .stats-card {
      .stats-content {
        grid-template-columns: 1fr;
      }
    }
  }

  .log-card {
    .log-content {
      .log-viewer {
        height: 200px;
        font-size: 11px;
      }
    }
  }
}
</style>
