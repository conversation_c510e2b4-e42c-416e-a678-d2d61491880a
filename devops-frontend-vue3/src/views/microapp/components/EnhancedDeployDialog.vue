<template>
  <el-dialog
    :model-value="visible"
    title="微应用部署管理"
    width="90%"
    top="5vh"
    @update:model-value="$emit('update:visible', $event)"
  >
    <el-tabs v-model="activeTab" class="deploy-tabs">
      <!-- 新建部署 -->
      <el-tab-pane label="新建部署" name="deploy">
        <el-row :gutter="20">
          <el-col :span="14">
            <el-form :model="deployForm" :rules="rules" ref="formRef" label-width="120px">
              <el-form-item label="部署环境" prop="environment">
                <el-select v-model="deployForm.environment" placeholder="请选择部署环境" style="width: 100%">
                  <el-option label="开发环境" value="dev" />
                  <el-option label="测试环境" value="test" />
                  <el-option label="预发布环境" value="staging" />
                  <el-option label="生产环境" value="prod" />
                </el-select>
              </el-form-item>

              <el-form-item label="部署分支/标签" prop="version">
                <el-select v-model="deployForm.version" placeholder="请选择分支或标签" style="width: 100%">
                  <el-option-group label="分支">
                    <el-option
                      v-for="branch in branchOptions"
                      :key="branch.name"
                      :label="branch.name"
                      :value="branch.name"
                    />
                  </el-option-group>
                  <el-option-group label="标签">
                    <el-option
                      v-for="tag in tagOptions"
                      :key="tag.name"
                      :label="`${tag.name} - ${tag.message}`"
                      :value="tag.name"
                    />
                  </el-option-group>
                </el-select>
              </el-form-item>

              <el-form-item label="实例数量" prop="replicas">
                <el-input-number v-model="deployForm.replicas" :min="1" :max="20" style="width: 100%" />
              </el-form-item>

              <el-form-item label="部署策略" prop="strategy">
                <el-radio-group v-model="deployForm.strategy">
                  <el-radio value="rolling">滚动更新</el-radio>
                  <el-radio value="recreate">重新创建</el-radio>
                  <el-radio value="blue-green">蓝绿部署</el-radio>
                  <el-radio value="canary">金丝雀部署</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="高级配置">
                <el-checkbox v-model="deployForm.healthCheck">启用健康检查</el-checkbox>
                <el-checkbox v-model="deployForm.autoRollback" style="margin-left: 20px">自动回滚</el-checkbox>
                <el-checkbox v-model="deployForm.enableMetrics" style="margin-left: 20px">启用监控</el-checkbox>
              </el-form-item>

              <el-form-item label="部署描述" prop="description">
                <el-input
                  v-model="deployForm.description"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入部署描述"
                />
              </el-form-item>

              <!-- 环境变量覆盖 -->
              <el-form-item label="环境变量">
                <div class="env-variables">
                  <el-table :data="envOverrides" size="small" max-height="200">
                    <el-table-column prop="key" label="变量名" width="150">
                      <template #default="{ row }">
                        <el-input v-model="row.key" size="small" placeholder="变量名" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="value" label="变量值">
                      <template #default="{ row }">
                        <el-input v-model="row.value" size="small" placeholder="变量值" />
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80">
                      <template #default="{ $index }">
                        <el-button size="small" type="danger" @click="removeEnvOverride($index)">
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button size="small" type="primary" @click="addEnvOverride" style="margin-top: 10px;">
                    添加环境变量
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
          
          <el-col :span="10">
            <!-- 部署预览 -->
            <el-card class="deploy-preview" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>部署预览</span>
                </div>
              </template>
              <div class="preview-content">
                <div class="preview-item">
                  <span class="label">应用名称:</span>
                  <span class="value">{{ appData?.name || '-' }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">部署环境:</span>
                  <el-tag :type="getEnvTagType(deployForm.environment)">
                    {{ getEnvLabel(deployForm.environment) }}
                  </el-tag>
                </div>
                <div class="preview-item">
                  <span class="label">部署版本:</span>
                  <span class="value">{{ deployForm.version || '-' }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">实例数量:</span>
                  <span class="value">{{ deployForm.replicas }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">部署策略:</span>
                  <span class="value">{{ getStrategyLabel(deployForm.strategy) }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">预计耗时:</span>
                  <span class="value">{{ estimatedTime }}</span>
                </div>
              </div>
            </el-card>

            <!-- 部署检查 -->
            <el-card class="deploy-check" shadow="hover" style="margin-top: 20px">
              <template #header>
                <div class="card-header">
                  <span>部署检查</span>
                  <el-button size="small" @click="runPreCheck">运行检查</el-button>
                </div>
              </template>
              <div class="check-content">
                <div v-for="check in preChecks" :key="check.name" class="check-item">
                  <el-icon :class="getCheckIconClass(check.status)">
                    <Loading v-if="check.status === 'running'" />
                    <CircleCheck v-else-if="check.status === 'success'" />
                    <CircleClose v-else-if="check.status === 'error'" />
                    <Warning v-else-if="check.status === 'warning'" />
                    <Minus v-else />
                  </el-icon>
                  <span class="check-name">{{ check.name }}</span>
                  <span class="check-message">{{ check.message }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-tab-pane>

      <!-- 部署历史 -->
      <el-tab-pane label="部署历史" name="history">
        <div class="deploy-history">
          <div class="history-filters">
            <el-form inline>
              <el-form-item label="环境">
                <el-select v-model="historyFilter.environment" placeholder="全部环境" clearable>
                  <el-option label="开发环境" value="dev" />
                  <el-option label="测试环境" value="test" />
                  <el-option label="预发布环境" value="staging" />
                  <el-option label="生产环境" value="prod" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select v-model="historyFilter.status" placeholder="全部状态" clearable>
                  <el-option label="成功" value="success" />
                  <el-option label="失败" value="failed" />
                  <el-option label="进行中" value="running" />
                  <el-option label="已回滚" value="rollback" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="loadDeployHistory">查询</el-button>
                <el-button @click="resetHistoryFilter">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <el-table :data="deployHistory" v-loading="historyLoading">
            <el-table-column prop="id" label="部署ID" width="100" />
            <el-table-column prop="version" label="版本" width="120" />
            <el-table-column prop="environment" label="环境" width="100">
              <template #default="{ row }">
                <el-tag :type="getEnvTagType(row.environment)">
                  {{ getEnvLabel(row.environment) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusTagType(row.status)">
                  {{ getStatusLabel(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="replicas" label="实例数" width="80" />
            <el-table-column prop="strategy" label="策略" width="100" />
            <el-table-column prop="duration" label="耗时" width="80" />
            <el-table-column prop="created_by" label="部署人" width="100" />
            <el-table-column prop="created_at" label="部署时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="{ row }">
                <el-button size="small" @click="viewDeployDetail(row)">详情</el-button>
                <el-button 
                  size="small" 
                  type="warning" 
                  @click="rollbackDeploy(row)"
                  :disabled="row.status !== 'success'"
                >
                  回滚
                </el-button>
                <el-button 
                  size="small" 
                  type="danger" 
                  @click="stopDeploy(row)"
                  :disabled="row.status !== 'running'"
                >
                  停止
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-wrapper">
            <el-pagination
              :current-page="historyPagination.page"
              :page-size="historyPagination.pageSize"
              :total="historyPagination.total"
              layout="total, prev, pager, next"
              @current-change="handleHistoryPageChange"
            />
          </div>
        </div>
      </el-tab-pane>

      <!-- 实时监控 -->
      <el-tab-pane label="实时监控" name="monitor">
        <div class="deploy-monitor">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card class="monitor-card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>部署进度</span>
                    <el-tag :type="currentDeploy.status === 'running' ? 'warning' : 'success'">
                      {{ getStatusLabel(currentDeploy.status) }}
                    </el-tag>
                  </div>
                </template>
                <div class="progress-content">
                  <el-progress 
                    :percentage="currentDeploy.progress" 
                    :status="currentDeploy.status === 'success' ? 'success' : undefined"
                  />
                  <div class="progress-info">
                    <span>当前步骤: {{ currentDeploy.currentStep }}</span>
                    <span>已耗时: {{ currentDeploy.duration }}</span>
                  </div>
                </div>
              </el-card>
            </el-col>
            
            <el-col :span="12">
              <el-card class="monitor-card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <span>实例状态</span>
                    <el-button size="small" @click="refreshInstanceStatus">刷新</el-button>
                  </div>
                </template>
                <div class="instance-status">
                  <div v-for="instance in instanceStatus" :key="instance.name" class="instance-item">
                    <div class="instance-name">{{ instance.name }}</div>
                    <div class="instance-info">
                      <el-tag :type="instance.status === 'running' ? 'success' : 'danger'" size="small">
                        {{ instance.status }}
                      </el-tag>
                      <span class="instance-cpu">CPU: {{ instance.cpu }}%</span>
                      <span class="instance-memory">内存: {{ instance.memory }}%</span>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>

          <!-- 部署日志 -->
          <el-card class="log-card" shadow="hover" style="margin-top: 20px">
            <template #header>
              <div class="card-header">
                <span>部署日志</span>
                <div class="log-actions">
                  <el-button size="small" @click="refreshLogs">刷新</el-button>
                  <el-button size="small" @click="downloadLogs">下载</el-button>
                  <el-switch v-model="autoRefreshLogs" active-text="自动刷新" />
                </div>
              </div>
            </template>
            <div class="log-content">
              <div class="log-viewer" ref="logViewer">
                <div v-for="(log, index) in deployLogs" :key="index" class="log-line">
                  <span class="log-time">{{ log.timestamp }}</span>
                  <span :class="['log-level', `log-${log.level}`]">{{ log.level }}</span>
                  <span class="log-message">{{ log.message }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="$emit('update:visible', false)">关闭</el-button>
        <el-button 
          v-if="activeTab === 'deploy'"
          type="primary" 
          :loading="deployLoading" 
          @click="handleDeploy"
        >
          开始部署
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import {
  Loading, CircleCheck, CircleClose, Warning, Minus
} from '@element-plus/icons-vue'

interface Props {
  visible: boolean
  appData?: any
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()
const logViewer = ref<HTMLElement>()

// 状态管理
const activeTab = ref('deploy')
const deployLoading = ref(false)
const historyLoading = ref(false)
const autoRefreshLogs = ref(false)
let logRefreshTimer: NodeJS.Timeout | null = null

// 部署表单
const deployForm = reactive({
  environment: '',
  version: '',
  replicas: 1,
  strategy: 'rolling',
  healthCheck: true,
  autoRollback: true,
  enableMetrics: true,
  description: ''
})

// 环境变量覆盖
const envOverrides = ref([
  { key: '', value: '' }
])

// 分支和标签选项
const branchOptions = ref([
  { name: 'main', commit: 'abc123' },
  { name: 'develop', commit: 'def456' },
  { name: 'feature/new-ui', commit: 'ghi789' }
])

const tagOptions = ref([
  { name: 'v1.2.3', message: 'Release version 1.2.3' },
  { name: 'v1.2.2', message: 'Release version 1.2.2' },
  { name: 'v1.2.1', message: 'Release version 1.2.1' }
])

// 表单验证规则
const rules: FormRules = {
  environment: [
    { required: true, message: '请选择部署环境', trigger: 'change' }
  ],
  version: [
    { required: true, message: '请选择部署版本', trigger: 'change' }
  ],
  replicas: [
    { required: true, message: '请设置实例数量', trigger: 'blur' }
  ],
  strategy: [
    { required: true, message: '请选择部署策略', trigger: 'change' }
  ]
}

// 预检查项
const preChecks = ref([
  { name: '资源检查', status: 'pending', message: '检查集群资源是否充足' },
  { name: '镜像检查', status: 'pending', message: '检查镜像是否存在' },
  { name: '配置检查', status: 'pending', message: '检查配置文件是否正确' },
  { name: '依赖检查', status: 'pending', message: '检查服务依赖是否可用' },
  { name: '权限检查', status: 'pending', message: '检查部署权限' }
])

// 部署历史
const historyFilter = reactive({
  environment: '',
  status: ''
})

const historyPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

const deployHistory = ref([
  {
    id: 'deploy-001',
    version: 'v1.2.3',
    environment: 'prod',
    status: 'success',
    replicas: 3,
    strategy: 'rolling',
    duration: '5m 32s',
    created_by: 'admin',
    created_at: '2024-01-15 14:30:00'
  },
  {
    id: 'deploy-002',
    version: 'v1.2.2',
    environment: 'staging',
    status: 'failed',
    replicas: 2,
    strategy: 'recreate',
    duration: '2m 15s',
    created_by: 'developer',
    created_at: '2024-01-15 10:15:00'
  }
])

// 当前部署状态
const currentDeploy = reactive({
  status: 'running',
  progress: 65,
  currentStep: '正在更新Pod实例',
  duration: '3m 45s'
})

// 实例状态
const instanceStatus = ref([
  { name: 'pod-1', status: 'running', cpu: 45, memory: 60 },
  { name: 'pod-2', status: 'running', cpu: 38, memory: 55 },
  { name: 'pod-3', status: 'pending', cpu: 0, memory: 0 }
])

// 部署日志
const deployLogs = ref([
  { timestamp: '14:30:01', level: 'info', message: '开始部署应用...' },
  { timestamp: '14:30:05', level: 'info', message: '拉取镜像 nginx:1.20' },
  { timestamp: '14:30:15', level: 'info', message: '创建Pod实例' },
  { timestamp: '14:30:25', level: 'warn', message: '等待Pod就绪...' },
  { timestamp: '14:30:35', level: 'info', message: 'Pod实例启动成功' }
])

// 计算属性
const estimatedTime = computed(() => {
  const baseTime = 3 // 基础时间3分钟
  const replicaTime = deployForm.replicas * 0.5 // 每个实例增加30秒
  const strategyTime = deployForm.strategy === 'blue-green' ? 2 : 0 // 蓝绿部署增加2分钟
  return `约 ${Math.ceil(baseTime + replicaTime + strategyTime)} 分钟`
})

// 方法
const addEnvOverride = () => {
  envOverrides.value.push({ key: '', value: '' })
}

const removeEnvOverride = (index: number) => {
  envOverrides.value.splice(index, 1)
}

const getEnvTagType = (env: string) => {
  const typeMap: Record<string, string> = {
    'dev': 'info',
    'test': 'warning',
    'staging': 'primary',
    'prod': 'danger'
  }
  return typeMap[env] || 'info'
}

const getEnvLabel = (env: string) => {
  const labelMap: Record<string, string> = {
    'dev': '开发环境',
    'test': '测试环境',
    'staging': '预发布环境',
    'prod': '生产环境'
  }
  return labelMap[env] || env
}

const getStrategyLabel = (strategy: string) => {
  const labelMap: Record<string, string> = {
    'rolling': '滚动更新',
    'recreate': '重新创建',
    'blue-green': '蓝绿部署',
    'canary': '金丝雀部署'
  }
  return labelMap[strategy] || strategy
}

const getStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'success': 'success',
    'failed': 'danger',
    'running': 'warning',
    'rollback': 'info'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status: string) => {
  const labelMap: Record<string, string> = {
    'success': '成功',
    'failed': '失败',
    'running': '进行中',
    'rollback': '已回滚'
  }
  return labelMap[status] || status
}

const getCheckIconClass = (status: string) => {
  const classMap: Record<string, string> = {
    'running': 'check-running',
    'success': 'check-success',
    'error': 'check-error',
    'warning': 'check-warning',
    'pending': 'check-pending'
  }
  return classMap[status] || 'check-pending'
}

// 运行预检查
const runPreCheck = async () => {
  for (const check of preChecks.value) {
    check.status = 'running'
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 模拟检查结果
    const random = Math.random()
    if (random > 0.8) {
      check.status = 'error'
      check.message = '检查失败，请检查配置'
    } else if (random > 0.6) {
      check.status = 'warning'
      check.message = '检查通过，但有警告'
    } else {
      check.status = 'success'
      check.message = '检查通过'
    }
  }
}

// 处理部署
const handleDeploy = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    deployLoading.value = true

    // 模拟部署过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('部署任务已提交')
    activeTab.value = 'monitor'
    emit('success')
  } catch (error) {
    console.error('部署失败:', error)
  } finally {
    deployLoading.value = false
  }
}

// 加载部署历史
const loadDeployHistory = async () => {
  historyLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 这里应该调用实际的API
  } catch (error) {
    console.error('加载部署历史失败:', error)
  } finally {
    historyLoading.value = false
  }
}

// 重置历史筛选
const resetHistoryFilter = () => {
  historyFilter.environment = ''
  historyFilter.status = ''
  loadDeployHistory()
}

// 处理历史分页
const handleHistoryPageChange = (page: number) => {
  historyPagination.page = page
  loadDeployHistory()
}

// 查看部署详情
const viewDeployDetail = (deploy: any) => {
  ElMessage.info(`查看部署详情: ${deploy.id}`)
}

// 回滚部署
const rollbackDeploy = async (deploy: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要回滚到版本 ${deploy.version} 吗？`,
      '确认回滚',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('回滚任务已提交')
  } catch (error) {
    // 用户取消
  }
}

// 停止部署
const stopDeploy = async (deploy: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要停止部署 ${deploy.id} 吗？`,
      '确认停止',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('部署已停止')
  } catch (error) {
    // 用户取消
  }
}

// 刷新实例状态
const refreshInstanceStatus = async () => {
  // 模拟刷新
  ElMessage.success('实例状态已刷新')
}

// 刷新日志
const refreshLogs = async () => {
  // 模拟刷新日志
  deployLogs.value.push({
    timestamp: new Date().toLocaleTimeString(),
    level: 'info',
    message: '日志已刷新'
  })

  // 滚动到底部
  await nextTick()
  if (logViewer.value) {
    logViewer.value.scrollTop = logViewer.value.scrollHeight
  }
}

// 下载日志
const downloadLogs = () => {
  const logContent = deployLogs.value
    .map(log => `${log.timestamp} [${log.level.toUpperCase()}] ${log.message}`)
    .join('\n')

  const blob = new Blob([logContent], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `deploy-logs-${Date.now()}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 监听自动刷新日志
watch(autoRefreshLogs, (newVal) => {
  if (newVal) {
    logRefreshTimer = setInterval(refreshLogs, 5000)
  } else {
    if (logRefreshTimer) {
      clearInterval(logRefreshTimer)
      logRefreshTimer = null
    }
  }
})

// 生命周期
onMounted(() => {
  loadDeployHistory()
})

onUnmounted(() => {
  if (logRefreshTimer) {
    clearInterval(logRefreshTimer)
  }
})
</script>

<style scoped lang="scss">
.deploy-tabs {
  :deep(.el-tabs__header) {
    margin-bottom: 20px;
  }
}

.deploy-preview {
  .preview-content {
    .preview-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .label {
        font-weight: 500;
        color: #606266;
      }

      .value {
        color: #303133;
      }
    }
  }
}

.deploy-check {
  .check-content {
    .check-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .el-icon {
        margin-right: 8px;

        &.check-running {
          color: #409eff;
        }

        &.check-success {
          color: #67c23a;
        }

        &.check-error {
          color: #f56c6c;
        }

        &.check-warning {
          color: #e6a23c;
        }

        &.check-pending {
          color: #909399;
        }
      }

      .check-name {
        font-weight: 500;
        margin-right: 8px;
        min-width: 80px;
      }

      .check-message {
        color: #606266;
        font-size: 12px;
      }
    }
  }
}

.env-variables {
  .el-table {
    margin-bottom: 10px;
  }
}

.deploy-history {
  .history-filters {
    margin-bottom: 20px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
}

.deploy-monitor {
  .monitor-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .progress-content {
      .progress-info {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        font-size: 12px;
        color: #606266;
      }
    }

    .instance-status {
      .instance-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .instance-name {
          font-weight: 500;
        }

        .instance-info {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 12px;

          .instance-cpu,
          .instance-memory {
            color: #606266;
          }
        }
      }
    }
  }

  .log-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .log-actions {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    .log-content {
      .log-viewer {
        height: 300px;
        overflow-y: auto;
        background: #1e1e1e;
        color: #d4d4d4;
        padding: 10px;
        border-radius: 4px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;

        .log-line {
          margin-bottom: 2px;

          .log-time {
            color: #569cd6;
            margin-right: 8px;
          }

          .log-level {
            margin-right: 8px;
            padding: 2px 6px;
            border-radius: 2px;
            font-size: 10px;
            font-weight: bold;

            &.log-info {
              background: #4ec9b0;
              color: #1e1e1e;
            }

            &.log-warn {
              background: #dcdcaa;
              color: #1e1e1e;
            }

            &.log-error {
              background: #f44747;
              color: #ffffff;
            }

            &.log-debug {
              background: #9cdcfe;
              color: #1e1e1e;
            }
          }

          .log-message {
            color: #d4d4d4;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

// 响应式设计
@media (max-width: 768px) {
  .deploy-tabs {
    :deep(.el-tab-pane) {
      padding: 0 10px;
    }
  }

  .deploy-preview,
  .deploy-check {
    margin-top: 20px;
  }

  .deploy-monitor {
    .el-row {
      .el-col {
        margin-bottom: 20px;
      }
    }
  }

  .log-card {
    .log-content {
      .log-viewer {
        height: 200px;
        font-size: 11px;
      }
    }
  }
}
</style>
