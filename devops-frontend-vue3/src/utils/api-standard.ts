// API标准化工具
import { ElMessage } from 'element-plus'

// 统一的API响应接口
export interface APIResponse<T = any> {
    code: number
    message: string
    data?: T
}

// 分页响应接口
export interface PaginatedResponse<T = any> {
    code: number
    message: string
    data: {
        items: T[]
    }
    total: number
    page: number
    page_size: number
}

// 基础模型接口
export interface BaseModel {
    id: number
    created_at: string
    updated_at: string
}

// 分页请求参数
export interface PaginationParams {
    page: number
    page_size: number
}

// 搜索请求参数
export interface SearchParams {
    search?: string
    name?: string
    keyword?: string
}

// 排序请求参数
export interface SortParams {
    sort_by?: string
    sort_order?: 'asc' | 'desc'
}

// 完整的列表请求参数
export interface ListRequestParams extends PaginationParams, SearchParams, SortParams {
    [key: string]: any
}

// 响应状态码常量
export const ResponseCodes = {
    SUCCESS: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    VALIDATION_ERROR: 422,
    INTERNAL_ERROR: 500,
    SERVICE_ERROR: 503
} as const

// 响应处理工具类
export class ResponseHelper {
    /**
     * 检查响应是否成功
     */
    static isSuccess(response: APIResponse): boolean {
        return response.code === ResponseCodes.SUCCESS || response.code === ResponseCodes.CREATED
    }

    /**
     * 检查分页响应是否成功
     */
    static isPaginatedSuccess(response: PaginatedResponse): boolean {
        return response.code === ResponseCodes.SUCCESS
    }

    /**
     * 处理API响应
     */
    static handleResponse<T>(response: APIResponse<T>): T | null {
        if (this.isSuccess(response)) {
            return response.data || null
        } else {
            this.handleError(response)
            return null
        }
    }

    /**
     * 处理分页响应
     */
    static handlePaginatedResponse<T>(response: PaginatedResponse<T>): {
        items: T[]
        total: number
        page: number
        pageSize: number
    } | null {
        if (this.isPaginatedSuccess(response)) {
            return {
                items: response.data.items,
                total: response.total,
                page: response.page,
                pageSize: response.page_size
            }
        } else {
            this.handleError(response)
            return null
        }
    }

    /**
     * 处理错误响应
     */
    static handleError(response: APIResponse | PaginatedResponse): void {
        const errorMessage = this.getErrorMessage(response.code, response.message)

        // 显示错误消息
        ElMessage.error(errorMessage)

        // 记录错误日志
        console.error('API Error:', {
            code: response.code,
            message: response.message,
            timestamp: new Date().toISOString()
        })
    }

    /**
     * 获取错误消息
     */
    static getErrorMessage(code: number, message?: string): string {
        const defaultMessages: Record<number, string> = {
            [ResponseCodes.BAD_REQUEST]: '请求参数错误',
            [ResponseCodes.UNAUTHORIZED]: '未授权，请重新登录',
            [ResponseCodes.FORBIDDEN]: '权限不足',
            [ResponseCodes.NOT_FOUND]: '资源不存在',
            [ResponseCodes.VALIDATION_ERROR]: '数据验证失败',
            [ResponseCodes.INTERNAL_ERROR]: '服务器内部错误',
            [ResponseCodes.SERVICE_ERROR]: '服务暂时不可用'
        }

        return message || defaultMessages[code] || `未知错误 (${code})`
    }

    /**
     * 显示成功消息
     */
    static showSuccess(message: string = '操作成功'): void {
        ElMessage.success(message)
    }

    /**
     * 显示警告消息
     */
    static showWarning(message: string): void {
        ElMessage.warning(message)
    }

    /**
     * 显示信息消息
     */
    static showInfo(message: string): void {
        ElMessage.info(message)
    }
}

// 请求参数构建器
export class RequestBuilder {
    private params: Record<string, any> = {}

    /**
     * 设置分页参数
     */
    pagination(page: number, pageSize: number): this {
        this.params.page = page
        this.params.page_size = pageSize
        return this
    }

    /**
     * 设置搜索参数
     */
    search(keyword: string, field: string = 'search'): this {
        if (keyword?.trim()) {
            this.params[field] = keyword.trim()
        }
        return this
    }

    /**
     * 设置排序参数
     */
    sort(field: string, order: 'asc' | 'desc' = 'asc'): this {
        this.params.sort_by = field
        this.params.sort_order = order
        return this
    }

    /**
     * 设置筛选参数
     */
    filter(key: string, value: any): this {
        if (value !== undefined && value !== null && value !== '') {
            this.params[key] = value
        }
        return this
    }

    /**
 * 批量设置参数
 */
    setParams(params: Record<string, any>): this {
        Object.assign(this.params, params)
        return this
    }

    /**
     * 构建最终参数
     */
    build(): Record<string, any> {
        return { ...this.params }
    }

    /**
     * 重置参数
     */
    reset(): this {
        this.params = {}
        return this
    }
}

// 数据转换工具
export class DataTransformer {
    /**
     * 转换日期格式
     */
    static formatDate(date: string | Date | null): string {
        if (!date) return '-'

        const d = new Date(date)
        if (isNaN(d.getTime())) return '-'

        return d.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        })
    }

    /**
     * 转换日期时间格式
     */
    static formatDateTime(date: string | Date | null): string {
        if (!date) return '-'

        const d = new Date(date)
        if (isNaN(d.getTime())) return '-'

        return d.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        })
    }

    /**
     * 转换相对时间
     */
    static formatRelativeTime(date: string | Date | null): string {
        if (!date) return '-'

        const d = new Date(date)
        if (isNaN(d.getTime())) return '-'

        const now = new Date()
        const diff = now.getTime() - d.getTime()

        const seconds = Math.floor(diff / 1000)
        const minutes = Math.floor(seconds / 60)
        const hours = Math.floor(minutes / 60)
        const days = Math.floor(hours / 24)

        if (days > 0) return `${days}天前`
        if (hours > 0) return `${hours}小时前`
        if (minutes > 0) return `${minutes}分钟前`
        return '刚刚'
    }

    /**
     * 转换文件大小
     */
    static formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 B'

        const k = 1024
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    /**
     * 转换状态显示
     */
    static formatStatus(status: string | number): {
        text: string
        type: 'success' | 'warning' | 'danger' | 'info'
    } {
        const statusMap: Record<string, { text: string; type: 'success' | 'warning' | 'danger' | 'info' }> = {
            '1': { text: '启用', type: 'success' },
            '0': { text: '禁用', type: 'danger' },
            'active': { text: '活跃', type: 'success' },
            'inactive': { text: '非活跃', type: 'warning' },
            'pending': { text: '待处理', type: 'warning' },
            'processing': { text: '处理中', type: 'info' },
            'completed': { text: '已完成', type: 'success' },
            'failed': { text: '失败', type: 'danger' }
        }

        const key = String(status).toLowerCase()
        return statusMap[key] || { text: String(status), type: 'info' }
    }

    /**
     * 转换布尔值显示
     */
    static formatBoolean(value: boolean | number | string): string {
        if (typeof value === 'boolean') {
            return value ? '是' : '否'
        }
        if (typeof value === 'number') {
            return value ? '是' : '否'
        }
        if (typeof value === 'string') {
            const lowerValue = value.toLowerCase()
            return ['true', '1', 'yes', 'on'].includes(lowerValue) ? '是' : '否'
        }
        return '否'
    }

    /**
     * 安全获取嵌套属性
     */
    static safeGet(obj: any, path: string, defaultValue: any = '-'): any {
        if (!obj || !path) return defaultValue

        const keys = path.split('.')
        let result = obj

        for (const key of keys) {
            if (result === null || result === undefined) {
                return defaultValue
            }
            result = result[key]
        }

        return result !== undefined ? result : defaultValue
    }

    /**
     * 截断文本
     */
    static truncateText(text: string, maxLength: number = 50): string {
        if (!text || text.length <= maxLength) return text
        return text.substring(0, maxLength) + '...'
    }
}

// 验证工具
export class ValidationHelper {
    /**
     * 验证必填字段
     */
    static required(value: any, message: string = '此字段为必填项'): string | null {
        if (value === null || value === undefined || value === '') {
            return message
        }
        return null
    }

    /**
     * 验证最小长度
     */
    static minLength(value: string, min: number, message?: string): string | null {
        if (!value || value.length < min) {
            return message || `最少需要${min}个字符`
        }
        return null
    }

    /**
     * 验证最大长度
     */
    static maxLength(value: string, max: number, message?: string): string | null {
        if (value && value.length > max) {
            return message || `最多允许${max}个字符`
        }
        return null
    }

    /**
     * 验证邮箱格式
     */
    static email(value: string, message: string = '请输入有效的邮箱地址'): string | null {
        if (!value) return null

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(value)) {
            return message
        }
        return null
    }

    /**
     * 验证手机号格式
     */
    static phone(value: string, message: string = '请输入有效的手机号'): string | null {
        if (!value) return null

        const phoneRegex = /^1[3-9]\d{9}$/
        if (!phoneRegex.test(value)) {
            return message
        }
        return null
    }

    /**
     * 验证URL格式
     */
    static url(value: string, message: string = '请输入有效的URL'): string | null {
        if (!value) return null

        try {
            new URL(value)
            return null
        } catch {
            return message
        }
    }

    /**
     * 验证数字范围
     */
    static numberRange(value: number, min?: number, max?: number, message?: string): string | null {
        if (value === null || value === undefined) return null

        if (min !== undefined && value < min) {
            return message || `数值不能小于${min}`
        }

        if (max !== undefined && value > max) {
            return message || `数值不能大于${max}`
        }

        return null
    }
}

// 导出便捷函数
export const isAPISuccess = ResponseHelper.isSuccess
export const handleAPIResponse = ResponseHelper.handleResponse
export const handlePaginatedResponse = ResponseHelper.handlePaginatedResponse
export const showSuccess = ResponseHelper.showSuccess
export const showError = ResponseHelper.handleError
export const buildRequest = () => new RequestBuilder()
export const formatDate = DataTransformer.formatDate
export const formatDateTime = DataTransformer.formatDateTime
export const formatStatus = DataTransformer.formatStatus
export const safeGet = DataTransformer.safeGet 