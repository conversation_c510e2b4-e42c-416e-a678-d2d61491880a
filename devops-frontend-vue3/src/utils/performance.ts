// 性能优化工具集合
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

// 缓存管理器
export class MemoryCache {
    private cache = new Map<string, { data: any; timestamp: number; ttl: number }>()
    private cleanupTimer: number | null = null

    constructor(private defaultTTL: number = 5 * 60 * 1000) { // 默认5分钟
        this.startCleanup()
    }

    /**
     * 设置缓存
     */
    set(key: string, data: any, ttl?: number): void {
        const expiration = ttl || this.defaultTTL
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
            ttl: expiration
        })
    }

    /**
     * 获取缓存
     */
    get<T = any>(key: string): T | null {
        const item = this.cache.get(key)
        if (!item) return null

        const now = Date.now()
        if (now - item.timestamp > item.ttl) {
            this.cache.delete(key)
            return null
        }

        return item.data
    }

    /**
     * 删除缓存
     */
    delete(key: string): boolean {
        return this.cache.delete(key)
    }

    /**
     * 清空所有缓存
     */
    clear(): void {
        this.cache.clear()
    }

    /**
     * 获取缓存大小
     */
    size(): number {
        return this.cache.size
    }

    /**
     * 检查缓存是否存在
     */
    has(key: string): boolean {
        const item = this.cache.get(key)
        if (!item) return false

        const now = Date.now()
        if (now - item.timestamp > item.ttl) {
            this.cache.delete(key)
            return false
        }

        return true
    }

    /**
 * 启动清理定时器
 */
    private startCleanup(): void {
        this.cleanupTimer = window.setInterval(() => {
            const now = Date.now()
            for (const [key, item] of this.cache.entries()) {
                if (now - item.timestamp > item.ttl) {
                    this.cache.delete(key)
                }
            }
        }, 60000) // 每分钟清理一次
    }

    /**
     * 销毁缓存管理器
     */
    destroy(): void {
        if (this.cleanupTimer) {
            window.clearInterval(this.cleanupTimer)
            this.cleanupTimer = null
        }
        this.cache.clear()
    }
}

// 全局缓存实例
export const globalCache = new MemoryCache()

// 防抖函数
export function debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate?: boolean
): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout | null = null

    return function executedFunction(...args: Parameters<T>) {
        const later = () => {
            timeout = null
            if (!immediate) func(...args)
        }

        const callNow = immediate && !timeout

        if (timeout) clearTimeout(timeout)
        timeout = setTimeout(later, wait)

        if (callNow) func(...args)
    }
}

// 节流函数
export function throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
): (...args: Parameters<T>) => void {
    let inThrottle: boolean = false

    return function executedFunction(...args: Parameters<T>) {
        if (!inThrottle) {
            func(...args)
            inThrottle = true
            setTimeout(() => inThrottle = false, limit)
        }
    }
}

// 虚拟滚动 Hook
export function useVirtualScroll(options: {
    itemHeight: number
    containerHeight: number
    overscan?: number
}) {
    const { itemHeight, containerHeight, overscan = 5 } = options

    const scrollTop = ref(0)
    const totalItems = ref(0)

    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const startIndex = computed(() => Math.max(0, Math.floor(scrollTop.value / itemHeight) - overscan))
    const endIndex = computed(() => Math.min(totalItems.value - 1, startIndex.value + visibleCount + overscan * 2))

    const visibleItems = computed(() => {
        const items: number[] = []
        for (let i = startIndex.value; i <= endIndex.value; i++) {
            items.push(i)
        }
        return items
    })

    const totalHeight = computed(() => totalItems.value * itemHeight)
    const offsetY = computed(() => startIndex.value * itemHeight)

    const handleScroll = throttle((event: Event) => {
        const target = event.target as HTMLElement
        scrollTop.value = target.scrollTop
    }, 16) // 60fps

    return {
        scrollTop,
        totalItems,
        visibleItems,
        totalHeight,
        offsetY,
        handleScroll,
        startIndex,
        endIndex
    }
}

// 懒加载 Hook
export function useLazyLoad(options: {
    threshold?: number
    rootMargin?: string
    root?: Element | null
} = {}) {
    const { threshold = 0.1, rootMargin = '50px', root = null } = options

    const isVisible = ref(false)
    const targetRef = ref<Element | null>(null)
    let observer: IntersectionObserver | null = null

    const observe = () => {
        if (!targetRef.value) return

        observer = new IntersectionObserver(
            ([entry]) => {
                isVisible.value = entry.isIntersecting
            },
            {
                threshold,
                rootMargin,
                root
            }
        )

        observer.observe(targetRef.value)
    }

    const unobserve = () => {
        if (observer && targetRef.value) {
            observer.unobserve(targetRef.value)
            observer.disconnect()
            observer = null
        }
    }

    onMounted(() => {
        nextTick(() => {
            observe()
        })
    })

    onUnmounted(() => {
        unobserve()
    })

    return {
        isVisible,
        targetRef,
        observe,
        unobserve
    }
}

// 图片懒加载 Hook
export function useImageLazyLoad() {
    const { isVisible, targetRef } = useLazyLoad({
        threshold: 0.1,
        rootMargin: '100px'
    })

    const imageRef = ref<HTMLImageElement | null>(null)
    const isLoaded = ref(false)
    const isError = ref(false)

    const loadImage = (src: string) => {
        if (!src || isLoaded.value) return

        const img = new Image()
        img.onload = () => {
            if (imageRef.value) {
                imageRef.value.src = src
                isLoaded.value = true
            }
        }
        img.onerror = () => {
            isError.value = true
        }
        img.src = src
    }

    return {
        isVisible,
        targetRef,
        imageRef,
        isLoaded,
        isError,
        loadImage
    }
}

// 性能监控器
export class PerformanceMonitor {
    private marks = new Map<string, number>()
    private measures = new Map<string, number>()

    /**
     * 开始性能标记
     */
    mark(name: string): void {
        this.marks.set(name, performance.now())
        if (performance.mark) {
            performance.mark(name)
        }
    }

    /**
     * 测量性能
     */
    measure(name: string, startMark: string, endMark?: string): number {
        const startTime = this.marks.get(startMark)
        if (!startTime) {
            console.warn(`Performance mark "${startMark}" not found`)
            return 0
        }

        const endTime = endMark ? this.marks.get(endMark) : performance.now()
        if (!endTime) {
            console.warn(`Performance mark "${endMark}" not found`)
            return 0
        }

        const duration = endTime - startTime
        this.measures.set(name, duration)

        if (performance.measure) {
            try {
                performance.measure(name, startMark, endMark)
            } catch (error) {
                console.warn('Performance measure failed:', error)
            }
        }

        return duration
    }

    /**
     * 获取测量结果
     */
    getMeasure(name: string): number | undefined {
        return this.measures.get(name)
    }

    /**
     * 清除标记
     */
    clearMarks(): void {
        this.marks.clear()
        if (performance.clearMarks) {
            performance.clearMarks()
        }
    }

    /**
     * 清除测量
     */
    clearMeasures(): void {
        this.measures.clear()
        if (performance.clearMeasures) {
            performance.clearMeasures()
        }
    }

    /**
     * 获取页面加载性能
     */
    getPageLoadMetrics(): {
        navigationStart: number
        domContentLoaded: number
        loadComplete: number
        firstPaint?: number
        firstContentfulPaint?: number
    } | null {
        if (!performance.timing) return null

        const timing = performance.timing
        const navigationStart = timing.navigationStart

        const metrics = {
            navigationStart: 0,
            domContentLoaded: timing.domContentLoadedEventEnd - navigationStart,
            loadComplete: timing.loadEventEnd - navigationStart
        }

        // 获取 Paint Timing API 数据
        if (performance.getEntriesByType) {
            const paintEntries = performance.getEntriesByType('paint')
            const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
            const firstContentfulPaint = paintEntries.find(entry => entry.name === 'first-contentful-paint')

            if (firstPaint) {
                (metrics as any).firstPaint = firstPaint.startTime
            }
            if (firstContentfulPaint) {
                (metrics as any).firstContentfulPaint = firstContentfulPaint.startTime
            }
        }

        return metrics
    }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

// 批处理执行器
export class BatchProcessor<T> {
    private queue: T[] = []
    private timer: number | null = null
    private isProcessing = false

    constructor(
        private processor: (items: T[]) => Promise<void> | void,
        private batchSize: number = 10,
        private delay: number = 100
    ) { }

    /**
     * 添加项目到批处理队列
     */
    add(item: T): void {
        this.queue.push(item)
        this.scheduleProcess()
    }

    /**
     * 批量添加项目
     */
    addBatch(items: T[]): void {
        this.queue.push(...items)
        this.scheduleProcess()
    }

    /**
     * 调度处理
     */
    private scheduleProcess(): void {
        if (this.isProcessing) return

        if (this.timer) {
            window.clearTimeout(this.timer)
        }

        this.timer = window.setTimeout(() => {
            this.process()
        }, this.delay)
    }

    /**
     * 处理队列
     */
    private async process(): Promise<void> {
        if (this.isProcessing || this.queue.length === 0) return

        this.isProcessing = true

        try {
            while (this.queue.length > 0) {
                const batch = this.queue.splice(0, this.batchSize)
                await this.processor(batch)
            }
        } finally {
            this.isProcessing = false
        }
    }

    /**
     * 立即处理所有项目
     */
    async flush(): Promise<void> {
        if (this.timer) {
            clearTimeout(this.timer)
            this.timer = null
        }
        await this.process()
    }

    /**
     * 清空队列
     */
    clear(): void {
        this.queue = []
        if (this.timer) {
            clearTimeout(this.timer)
            this.timer = null
        }
    }

    /**
     * 获取队列长度
     */
    size(): number {
        return this.queue.length
    }
}

// 资源预加载器
export class ResourcePreloader {
    private cache = new Set<string>()

    /**
     * 预加载图片
     */
    preloadImage(src: string): Promise<void> {
        if (this.cache.has(src)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const img = new Image()
            img.onload = () => {
                this.cache.add(src)
                resolve()
            }
            img.onerror = reject
            img.src = src
        })
    }

    /**
     * 批量预加载图片
     */
    async preloadImages(srcs: string[]): Promise<void> {
        const promises = srcs.map(src => this.preloadImage(src))
        await Promise.all(promises)
    }

    /**
     * 预加载脚本
     */
    preloadScript(src: string): Promise<void> {
        if (this.cache.has(src)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'script'
            link.href = src
            link.onload = () => {
                this.cache.add(src)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }

    /**
     * 预加载样式
     */
    preloadStyle(href: string): Promise<void> {
        if (this.cache.has(href)) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link')
            link.rel = 'preload'
            link.as = 'style'
            link.href = href
            link.onload = () => {
                this.cache.add(href)
                resolve()
            }
            link.onerror = reject
            document.head.appendChild(link)
        })
    }

    /**
     * 检查资源是否已缓存
     */
    isCached(src: string): boolean {
        return this.cache.has(src)
    }

    /**
     * 清空缓存
     */
    clearCache(): void {
        this.cache.clear()
    }
}

// 全局资源预加载实例
export const resourcePreloader = new ResourcePreloader()

// 缓存请求 Hook
export function useCachedRequest<T>(
    key: string,
    requestFn: () => Promise<T>,
    ttl?: number
) {
    const loading = ref(false)
    const error = ref<Error | null>(null)
    const data = ref<T | null>(null)

    const execute = async (forceRefresh = false) => {
        if (!forceRefresh) {
            const cached = globalCache.get<T>(key)
            if (cached) {
                data.value = cached
                return cached
            }
        }

        loading.value = true
        error.value = null

        try {
            const result = await requestFn()
            data.value = result
            globalCache.set(key, result, ttl)
            return result
        } catch (err) {
            error.value = err as Error
            throw err
        } finally {
            loading.value = false
        }
    }

    return {
        loading,
        error,
        data,
        execute
    }
}

// 防抖搜索 Hook
export function useDebouncedSearch(
    searchFn: (query: string) => Promise<any>,
    delay: number = 300
) {
    const query = ref('')
    const loading = ref(false)
    const results = ref<any[]>([])
    const error = ref<Error | null>(null)

    const debouncedSearch = debounce(async (searchQuery: string) => {
        if (!searchQuery.trim()) {
            results.value = []
            return
        }

        loading.value = true
        error.value = null

        try {
            const data = await searchFn(searchQuery)
            results.value = data
        } catch (err) {
            error.value = err as Error
            results.value = []
        } finally {
            loading.value = false
        }
    }, delay)

    const search = (searchQuery: string) => {
        query.value = searchQuery
        debouncedSearch(searchQuery)
    }

    return {
        query,
        loading,
        results,
        error,
        search
    }
}

// 导出常用实例
export { performanceMonitor as monitor }
export { globalCache as cache }
export { resourcePreloader as preloader } 