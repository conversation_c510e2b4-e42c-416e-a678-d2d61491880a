package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/devops-microservices/cmdb-service/config"
	"github.com/devops-microservices/cmdb-service/models"
	"github.com/devops-microservices/cmdb-service/services"
	"github.com/devops-microservices/cmdb-service/utils"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
)

type KubernetesController struct {
	db       *gorm.DB
	cfg      *config.Config
	auditSvc services.AuditService
	wsSvc    services.WebSocketService
}

func NewKubernetesController(db *gorm.DB, cfg *config.Config, auditSvc services.AuditService, wsSvc services.WebSocketService) *KubernetesController {
	return &KubernetesController{
		db:       db,
		cfg:      cfg,
		auditSvc: auditSvc,
		wsSvc:    wsSvc,
	}
}

// GetKubernetesClusters 获取K8s集群列表
// @Tags Kubernetes
// @Summary 获取K8s集群列表
// @Description 获取所有K8s集群的列表信息
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页大小" default(10)
// @Param search query string false "搜索关键字"
// @Param environment query string false "环境筛选"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/kubernetes [get]
func (c *KubernetesController) GetKubernetesClusters(ctx *gin.Context) {
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(ctx.DefaultQuery("page_size", "10"))
	search := ctx.Query("search")
	environment := ctx.Query("environment")
	forceStr := ctx.Query("force")

	// 日志请求参数，便于排查问题
	fmt.Printf("查询参数: page=%d, pageSize=%d, search=%s, environment=%s, force=%s\n",
		page, pageSize, search, environment, forceStr)

	// 基本查询
	var clusters []models.KubernetesCluster
	var total int64

	// 构建基础查询
	query := c.db.Model(&models.KubernetesCluster{})

	// 搜索条件
	if search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 环境筛选
	if environment != "" {
		// 使用子查询找到特定环境ID的集群
		var envClusterIDs []uint
		subQuery := c.db.Table("cmdb_kubernetesclusters_environment").
			Where("environment_id = ?", environment).
			Pluck("kubernetesclusters_id", &envClusterIDs)

		if subQuery.Error != nil {
			fmt.Printf("环境查询失败: %v\n", subQuery.Error)
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": "环境查询失败: " + subQuery.Error.Error(),
			})
			return
		}

		if len(envClusterIDs) > 0 {
			query = query.Where("id IN ?", envClusterIDs)
		} else {
			// 如果没有匹配的集群，返回空结果
			ctx.JSON(http.StatusOK, gin.H{
				"code": 20000,
				"data": gin.H{
					"total": 0,
					"items": []interface{}{},
				},
			})
			return
		}
	}

	// 计算总数
	if err := query.Count(&total).Error; err != nil {
		fmt.Printf("计算总数失败: %v\n", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "计算K8s集群总数失败: " + err.Error(),
		})
		return
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Order("sort_order ASC").Offset(offset).Limit(pageSize).Find(&clusters).Error; err != nil {
		fmt.Printf("查询失败: %v\n", err)
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "查询K8s集群失败: " + err.Error(),
		})
		return
	}

	// 处理集群数据，添加关联数据
	var responseItems []map[string]interface{}
	for _, cluster := range clusters {
		// 加载DataCenter信息
		var dataCenter models.DataCenter
		if cluster.DataCenterID != nil {
			c.db.First(&dataCenter, *cluster.DataCenterID)
		}

		// 加载环境信息
		var environments []int
		c.db.Table("cmdb_kubernetesclusters_environment").
			Where("kubernetesclusters_id = ?", cluster.ID).
			Pluck("environment_id", &environments)

		// 加载产品信息
		var products []int
		c.db.Table("cmdb_kubernetesclusters_product").
			Where("kubernetesclusters_id = ?", cluster.ID).
			Pluck("product_id", &products)

		// 构建配置信息
		configData := make(map[string]interface{})
		if cluster.ConfigData.Data != nil {
			if config, ok := cluster.ConfigData.Data.(map[string]interface{}); ok {
				configData = config
			}
		}

		// 确保config字段格式正确
		if _, ok := configData["config"]; !ok {
			configData["config"] = ""
		}
		if _, ok := configData["password"]; !ok {
			configData["password"] = "******"
		}
		if _, ok := configData["type"]; !ok {
			configData["type"] = "config"
		}
		if _, ok := configData["username"]; !ok {
			configData["username"] = "******"
		}

		// 构建扩展信息
		extraData := make(map[string]interface{})
		if cluster.ExtraData.Data != nil {
			if extra, ok := cluster.ExtraData.Data.(map[string]interface{}); ok {
				extraData = extra
			}
		}

		// 确保position字段存在
		if _, ok := extraData["position"]; !ok {
			extraData["position"] = []string{"dev", "qa", "op"}
		}

		// 构建版本信息
		versionData := make(map[string]interface{})
		if cluster.VersionInfo.Data != nil {
			if version, ok := cluster.VersionInfo.Data.(map[string]interface{}); ok {
				versionData = version
			} else {
				versionData = map[string]interface{}{
					"core":     "v1.30.5",
					"timezone": "",
					"api":      ""}
			}
		}

		// 构建完整的项
		item := map[string]interface{}{
			"id":           cluster.ID,
			"name":         cluster.Name,
			"cluster_code": cluster.ClusterCode,
			"description":  cluster.Description,
			"created_time": cluster.CreatedAt,
			"update_time":  cluster.UpdatedAt,
			"datacenter":   cluster.DataCenterID,
			"cluster_type": cluster.ClusterType,
			"sort_order":   cluster.SortOrder,
			"config":       configData,
			"environment":  environments,
			"product":      products,
			"extra":        extraData,
			"version_info": versionData,
		}

		responseItems = append(responseItems, item)
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": gin.H{
			"total": total,
			"items": responseItems,
		},
	})
}

// GetKubernetesCluster 获取单个K8s集群
// @Tags Kubernetes
// @Summary 获取单个K8s集群
// @Description 根据ID获取K8s集群详情
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/kubernetes/{id} [get]
func (c *KubernetesController) GetKubernetesCluster(ctx *gin.Context) {
	id := ctx.Param("id")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "K8s集群不存在",
		})
		return
	}

	decryptedConfig, err := utils.DecryptKubernetesConfig(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, cluster.ConfigData.Data)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "解密Kubernetes配置失败",
		})
		return
	}
	cluster.ConfigData.Data = decryptedConfig

	// 构建适合前端的响应格式
	responseData := map[string]interface{}{
		"id":           cluster.ID,
		"cluster_code": cluster.ClusterCode,
		"name":         cluster.Name,
		"description":  cluster.Description,
		"created_time": cluster.CreatedAt,
		"update_time":  cluster.UpdatedAt,
		"datacenter":   cluster.DataCenterID,
		"cluster_type": cluster.ClusterType,
		"sort_order":   cluster.SortOrder,
		"config":       cluster.ConfigData.Data,
		"extra":        cluster.ExtraData.Data,
		"version_info": cluster.VersionInfo.Data,
	}

	// 添加多对多关系数据
	var environments []int
	var products []int

	for _, env := range cluster.Environments {
		environments = append(environments, int(env.ID))
	}

	for _, product := range cluster.Products {
		products = append(products, int(product.ID))
	}

	responseData["environment"] = environments
	responseData["product"] = products

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": responseData,
	})
}

// CreateKubernetesCluster 创建K8s集群
// @Tags Kubernetes
// @Summary 创建K8s集群
// @Description 创建新的K8s集群
// @Accept json
// @Produce json
// @Param cluster body models.KubernetesCluster true "集群信息"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/kubernetes [post]
func (c *KubernetesController) CreateKubernetesCluster(ctx *gin.Context) {
	var cluster models.KubernetesCluster
	if err := ctx.ShouldBindJSON(&cluster); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	encryptedConfig, err := utils.EncryptKubernetesConfig(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, cluster.ConfigData.Data)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "加密Kubernetes配置失败",
		})
		return
	}
	cluster.ConfigData.Data = encryptedConfig

	if err := c.db.Create(&cluster).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "创建K8s集群失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": cluster,
	})
}

// UpdateKubernetesCluster 更新K8s集群
// @Tags Kubernetes
// @Summary 更新K8s集群
// @Description 更新K8s集群信息
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param cluster body models.KubernetesCluster true "集群信息"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/kubernetes/{id} [put]
func (c *KubernetesController) UpdateKubernetesCluster(ctx *gin.Context) {
	id := ctx.Param("id")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "K8s集群不存在",
		})
		return
	}

	if err := ctx.ShouldBindJSON(&cluster); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	encryptedConfig, err := utils.EncryptKubernetesConfig(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, cluster.ConfigData.Data)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "加密Kubernetes配置失败",
		})
		return
	}
	cluster.ConfigData.Data = encryptedConfig

	if err := c.db.Save(&cluster).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "更新K8s集群失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": cluster,
	})
}

// DeleteKubernetesCluster 删除K8s集群
// @Tags Kubernetes
// @Summary 删除K8s集群
// @Description 删除K8s集群
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/kubernetes/{id} [delete]
func (c *KubernetesController) DeleteKubernetesCluster(ctx *gin.Context) {
	id := ctx.Param("id")

	if err := c.db.Delete(&models.KubernetesCluster{}, id).Error; err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": "删除K8s集群失败",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "删除成功",
	})
}

// GetKubernetesConfig 获取K8s集群配置
// @Tags Kubernetes
// @Summary 获取K8s集群配置
// @Description 获取K8s集群的配置信息(需要管理员权限)
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Router /api/v1/cmdb/kubernetes/{id}/config [get]
func (c *KubernetesController) GetKubernetesConfig(ctx *gin.Context) {
	id := ctx.Param("id")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "K8s集群不存在",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code": 20000,
		"data": cluster.ConfigData.Data,
	})
}

// GetKubernetesPods 获取Kubernetes集群Pods
// @Tags Kubernetes
// @Summary 获取Kubernetes集群Pods
// @Description 获取Kubernetes集群的Pods信息
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param namespace query string false "命名空间"
// @Param limit query int false "每页限制数量" default(10)
// @Param _continue query string false "继续查询标记"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/pods [get]
func (c *KubernetesController) GetKubernetesPods(ctx *gin.Context) {
	id := ctx.Param("id")
	namespace := ctx.Query("namespace")
	name := ctx.Query("name")
	// status := ctx.Query("status")
	// node := ctx.Query("node")
	limitStr := ctx.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)
	continueToken := ctx.Query("_continue")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40400,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	result, err := k8sClient.GetPods(namespace, limit, continueToken, "", name)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("获取Pods失败: %v", err),
			"status":  "failed",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   result,
		"status": "success",
	})
}

// GetKubernetesStat 获取K8s集群统计信息
// @Tags Kubernetes
// @Summary 获取K8s集群统计信息
// @Description 获取K8s集群的统计信息
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param type query string true "统计类型(nodes/namespaces/services/deployments/pods)"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/stats [get]
func (c *KubernetesController) GetKubernetesStat(ctx *gin.Context) {
	id := ctx.Param("id")
	infoType := ctx.Query("type")
	namespaces := ctx.QueryArray("namespaces[]")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 获取所有命名空间
	nsList, err := k8sClient.GetNamespaces(200, "")
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("获取命名空间失败: %v", err),
			"status":  "failed",
		})
		return
	}
	nsItems, _ := nsList["items"].([]v1.Namespace)
	if len(namespaces) == 0 {
		for _, ns := range nsItems {
			namespaces = append(namespaces, ns.Name)
		}
	}

	switch infoType {
	case "nodes":
		nodes, err := k8sClient.GetNodes(200, "")
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": fmt.Sprintf("获取节点列表失败: %v", err),
				"status":  "failed",
			})
			return
		}
		nodeItems, _ := nodes["items"].([]v1.Node)
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"items": nodeItems,
				"total": len(nodeItems),
			},
		})
		return
	case "pods":
		totalPods := 0
		runningPods := 0
		for _, ns := range namespaces {
			pods, err := k8sClient.GetPods(ns, 500, "", "", "")
			if err == nil {
				podItems, _ := pods["items"].([]v1.Pod)
				totalPods += len(podItems)
				for _, p := range podItems {
					if p.Status.Phase == "Running" {
						runningPods++
					}
				}
			}
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"total_pods":   totalPods,
				"running_pods": runningPods,
			},
		})
		return
	case "deployments":
		totalDeployments := 0
		readyDeployments := 0
		// allDeployments := []interface{}{}
		fmt.Println("namespaces########", namespaces)
		for _, ns := range namespaces {
			deploys, err := k8sClient.GetDeployments(ns, cluster.VersionInfo.Data.(map[string]interface{})["api"].(string), 500, "")
			if err == nil {
				deployItems, _ := deploys["items"].([]appsv1.Deployment)
				totalDeployments += len(deployItems)
				for _, d := range deployItems {
					if d.Status.ReadyReplicas > 0 {
						readyDeployments++
					}
				}
			}
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"total_deployments": totalDeployments,
				"ready_deployments": readyDeployments,
			},
		})
		return
	case "services":
		totalServices := 0
		exposedServices := 0
		for _, ns := range namespaces {
			services, err := k8sClient.GetServices(ns, 500, "")
			if err == nil {
				serviceItems, _ := services["items"].([]v1.Service)
				totalServices += len(serviceItems)
				for _, s := range serviceItems {
					if s.Spec.Type == "NodePort" || s.Spec.Type == "LoadBalancer" {
						exposedServices++
					}
				}
			}
		}
		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"total_services":   totalServices,
				"exposed_services": exposedServices,
			},
		})
		return
	case "pods_metric":
		metrics, err := k8sClient.GetEachPodsMetrics(namespaces)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, gin.H{
				"code":    50000,
				"message": fmt.Sprintf("获取Pod指标失败: %v", err),
				"status":  "failed",
			})
			return
		}

		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": metrics,
		})
		return
	case "usage":
		// 使用新的集群资源使用率获取方法
		usage, err := k8sClient.GetClusterResourceUsage(namespaces)
		if err != nil {
			// 如果获取失败，使用模拟数据
			fmt.Printf("警告: 获取集群资源使用率失败: %v，使用模拟数据\n", err)
			ctx.JSON(http.StatusOK, gin.H{
				"code": 20000,
				"data": gin.H{
					"cpu_usage":     60.0,
					"memory_usage":  65.0,
					"storage_usage": 45.0,
				},
			})
			return
		}

		ctx.JSON(http.StatusOK, gin.H{
			"code": 20000,
			"data": gin.H{
				"cpu_usage":     usage["cpu_usage_percent"],
				"memory_usage":  usage["memory_usage_percent"],
				"storage_usage": usage["storage_usage_percent"],
				"details":       usage,
			},
		})
		return
	default:
		// 不支持的类型
		ctx.JSON(http.StatusOK, gin.H{
			"code":    50000,
			"message": "不支持的统计类型",
			"status":  "failed",
		})
		return
	}
}

// parseCPU 将K8s CPU字符串（如"250m"或"4"）转为 float64（核数）
func parseCPU(cpu string) float64 {
	if cpu == "" {
		return 0
	}
	if strings.HasSuffix(cpu, "m") {
		val, _ := strconv.ParseFloat(strings.TrimSuffix(cpu, "m"), 64)
		return val / 1000.0
	}
	val, _ := strconv.ParseFloat(cpu, 64)
	return val
}

// parseMemory 将K8s内存字符串（如"8192Mi"）转为 float64（MiB）
func parseMemory(mem string) float64 {
	if mem == "" {
		return 0
	}
	if strings.HasSuffix(mem, "Ki") {
		val, _ := strconv.ParseFloat(strings.TrimSuffix(mem, "Ki"), 64)
		return val / 1024.0
	}
	if strings.HasSuffix(mem, "Mi") {
		val, _ := strconv.ParseFloat(strings.TrimSuffix(mem, "Mi"), 64)
		return val
	}
	if strings.HasSuffix(mem, "Gi") {
		val, _ := strconv.ParseFloat(strings.TrimSuffix(mem, "Gi"), 64)
		return val * 1024.0
	}
	val, _ := strconv.ParseFloat(mem, 64)
	return val
}

// GetKubernetesInfo 获取Kubernetes集群详细信息
// @Tags Kubernetes
// @Summary 获取Kubernetes集群详细信息
// @Description 获取指定Kubernetes集群的节点、命名空间、服务等详细信息
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param type query string true "信息类型(nodes/namespaces/services/deployments/pods/configmap等)"
// @Param limit query int false "每页限制数量" default(10)
// @Param _continue query string false "继续查询标记"
// @Param namespaces[] query []string false "命名空间列表(pods类型使用)"
// @Param service query string false "服务名称(deployment_info/service_info类型使用)"
// @Param filter query bool false "服务过滤(services类型使用)"
// @Param force query bool false "强制刷新"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/info [get]
func (c *KubernetesController) GetKubernetesInfo(ctx *gin.Context) {
	id := ctx.Param("id")
	infoType := ctx.Query("type")
	namespaces := ctx.QueryArray("namespaces[]")
	service := ctx.Query("service")
	serviceFilter := ctx.DefaultQuery("filter", "false") == "true"
	forceStr := ctx.DefaultQuery("force", "0")
	force := forceStr != "0"
	limitStr := ctx.DefaultQuery("limit", "10")
	limit, _ := strconv.Atoi(limitStr)
	continueToken := ctx.Query("_continue")

	// 记录请求参数，便于调试
	fmt.Printf("获取Kubernetes信息: ID=%s, 类型=%s, 强制刷新=%v\n",
		id, infoType, force)

	// 获取集群信息
	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	// 创建Kubernetes客户端
	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	var result map[string]interface{}
	var fetchError error

	// 根据类型获取不同的信息
	switch infoType {
	case "nodes":
		result, fetchError = k8sClient.GetNodes(limit, continueToken)
	case "namespaces":
		result, fetchError = k8sClient.GetNamespaces(limit, continueToken)
	case "services":
		if len(namespaces) == 0 {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取服务需要指定命名空间",
				"status":  "failed",
			})
			return
		}

		// 如果启用了服务过滤且服务名不为空
		if serviceFilter && service != "" {
			// 应用标签选择器
			// TODO: 在utils.K8sClient中添加服务过滤功能
		}

		result, fetchError = k8sClient.GetServices(namespaces[0], limit, continueToken)
	case "deployments":
		if len(namespaces) == 0 {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取部署需要指定命名空间",
				"status":  "failed",
			})
			return
		}
		apiVersion := "apps/v1"
		if versionData, ok := cluster.VersionInfo.Data.(map[string]interface{}); ok {
			if v, exists := versionData["apiversion"]; exists && v != "" {
				apiVersion = v.(string)
			}
		}
		result, fetchError = k8sClient.GetDeployments(namespaces[0], apiVersion, limit, continueToken)
	case "deployment_info":
		if len(namespaces) == 0 || service == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取部署详情需要指定命名空间和服务名称",
				"status":  "failed",
			})
			return
		}
		apiVersion := "apps/v1"
		if versionData, ok := cluster.VersionInfo.Data.(map[string]interface{}); ok {
			if v, exists := versionData["apiversion"]; exists && v != "" {
				apiVersion = v.(string)
			}
		}
		result, fetchError = k8sClient.GetDeploymentInfo(namespaces[0], service, apiVersion)
	case "service_info":
		if len(namespaces) == 0 || service == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取服务详情需要指定命名空间和服务名称",
				"status":  "failed",
			})
			return
		}
		result, fetchError = k8sClient.GetServiceInfo(namespaces[0], service)
	case "deployment_pods":
		if len(namespaces) == 0 || service == "" {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取部署Pod需要指定命名空间和服务名称",
				"status":  "failed",
			})
			return
		}
		labelSelector := fmt.Sprintf("app=%s", service)
		result, fetchError = k8sClient.GetPods(namespaces[0], limit, continueToken, labelSelector, "")
	case "pods":
		if len(namespaces) == 0 {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取Pod需要指定命名空间",
				"status":  "failed",
			})
			return
		}
		// 合并多个命名空间的结果
		allItems := []interface{}{}
		metadata := map[string]interface{}{}

		for _, ns := range namespaces {
			nsResult, err := k8sClient.GetPods(ns, limit, continueToken, "", "")
			if err != nil {
				fetchError = err
				break
			}

			if items, ok := nsResult["items"].([]interface{}); ok {
				allItems = append(allItems, items...)
			}

			if nsResult["metadata"].(map[string]interface{})["continue"] != "" {
				metadata["continue"] = nsResult["metadata"].(map[string]interface{})["continue"]
			}
		}

		if fetchError == nil {
			result = map[string]interface{}{
				"items":    allItems,
				"metadata": metadata,
			}
		}
	case "pods_metric":
		if len(namespaces) == 0 {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取Pod指标需要指定命名空间",
				"status":  "failed",
			})
			return
		}
		result, fetchError = k8sClient.GetPodsMetrics(namespaces)
	case "configmap":
		if len(namespaces) == 0 {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": "获取ConfigMap需要指定命名空间",
				"status":  "failed",
			})
			return
		}
		result, fetchError = k8sClient.GetConfigMaps(namespaces[0], limit, continueToken)
	default:
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": fmt.Sprintf("不支持的信息类型: %s", infoType),
			"status":  "failed",
		})
		return
	}

	if fetchError != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("获取Kubernetes信息失败: %v", fetchError),
			"status":  "failed",
		})
		return
	}

	// 返回结果
	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   result,
		"status": "success",
	})
}

// GetKubernetesPod 获取单个Pod详情
// @Tags Kubernetes
// @Summary 获取单个Pod详情
// @Description 获取指定Kubernetes集群中单个Pod的详细信息
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "Pod名称"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/namespaces/{namespace}/pods/{name} [get]
func (c *KubernetesController) GetKubernetesPod(ctx *gin.Context) {
	id := ctx.Param("id")
	namespace := ctx.Param("namespace")
	name := ctx.Param("name")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	fieldSelector := fmt.Sprintf("metadata.name=%s", name)
	// 获取单个Pod
	result, err := k8sClient.GetPods(namespace, 1, "", "", fieldSelector)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("获取Pod失败: %v", err),
			"status":  "failed",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   result,
		"status": "success",
	})
}

// GetKubernetesPodLogs 获取Pod日志
// @Tags Kubernetes
// @Summary 获取Pod日志
// @Description 获取指定Kubernetes集群中Pod的日志
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "Pod名称"
// @Param container query string false "容器名称"
// @Param follow query bool false "是否跟踪日志"
// @Param tail_lines query int false "显示最后几行"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/namespaces/{namespace}/pods/{name}/logs [get]
func (c *KubernetesController) GetKubernetesPodLogs(ctx *gin.Context) {
	id := ctx.Param("id")
	namespace := ctx.Param("namespace")
	name := ctx.Param("name")
	container := ctx.Query("container")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 获取Pod日志
	logs, err := k8sClient.GetPodLogs(namespace, name, container)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("获取Pod日志失败: %v", err),
			"status":  "failed",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   logs,
		"status": "success",
	})
}

// DeleteKubernetesPod 删除Pod
// @Tags Kubernetes
// @Summary 删除Pod
// @Description 删除指定Kubernetes集群中的Pod
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param namespace path string true "命名空间"
// @Param name path string true "Pod名称"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/namespaces/{namespace}/pods/{name} [delete]
func (c *KubernetesController) DeleteKubernetesPod(ctx *gin.Context) {
	id := ctx.Param("id")
	namespace := ctx.Param("namespace")
	name := ctx.Param("name")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		// 记录失败的审计日志
		c.auditSvc.LogKubernetesOperation(ctx, cluster.ID, cluster.Name,
			models.AuditActionDelete, models.AuditResourcePod, name, namespace,
			fmt.Sprintf("删除Pod %s", name),
			map[string]interface{}{"error": err.Error()},
			models.AuditStatusFailed, err.Error())

		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 删除Pod
	err = k8sClient.DeletePod(namespace, name)
	if err != nil {
		// 记录失败的审计日志
		c.auditSvc.LogKubernetesOperation(ctx, cluster.ID, cluster.Name,
			models.AuditActionDelete, models.AuditResourcePod, name, namespace,
			fmt.Sprintf("删除Pod %s", name),
			map[string]interface{}{"error": err.Error()},
			models.AuditStatusFailed, err.Error())

		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("删除Pod失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 记录成功的审计日志
	c.auditSvc.LogKubernetesOperation(ctx, cluster.ID, cluster.Name,
		models.AuditActionDelete, models.AuditResourcePod, name, namespace,
		fmt.Sprintf("删除Pod %s", name),
		map[string]interface{}{"pod_name": name, "namespace": namespace},
		models.AuditStatusSuccess, "")

	// 发送WebSocket通知
	c.wsSvc.BroadcastToCluster(id, "pod_deleted", map[string]interface{}{
		"pod_name":  name,
		"namespace": namespace,
		"cluster":   cluster.Name,
	})

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "Pod删除成功",
		"status":  "success",
	})
}

// CreateKubernetesNamespace 创建命名空间
// @Tags Kubernetes
// @Summary 创建命名空间
// @Description 在指定Kubernetes集群中创建命名空间
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param data body map[string]interface{} true "命名空间数据"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/namespaces [post]
func (c *KubernetesController) CreateKubernetesNamespace(ctx *gin.Context) {
	id := ctx.Param("id")

	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 创建命名空间
	result, err := k8sClient.CreateNamespace(requestData)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("创建命名空间失败: %v", err),
			"status":  "failed",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   result,
		"status": "success",
	})
}

// CreateKubernetesService 创建服务
// @Tags Kubernetes
// @Summary 创建服务
// @Description 在指定Kubernetes集群中创建服务
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param namespace path string true "命名空间"
// @Param data body map[string]interface{} true "服务数据"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/namespaces/{namespace}/services [post]
func (c *KubernetesController) CreateKubernetesService(ctx *gin.Context) {
	id := ctx.Param("id")
	namespace := ctx.Param("namespace")

	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 创建服务
	result, err := k8sClient.CreateService(namespace, requestData)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("创建服务失败: %v", err),
			"status":  "failed",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   result,
		"status": "success",
	})
}

// CreateKubernetesConfigMap 创建ConfigMap
// @Tags Kubernetes
// @Summary 创建ConfigMap
// @Description 在指定Kubernetes集群中创建ConfigMap
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param namespace path string true "命名空间"
// @Param data body map[string]interface{} true "ConfigMap数据"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/namespaces/{namespace}/configmaps [post]
func (c *KubernetesController) CreateKubernetesConfigMap(ctx *gin.Context) {
	id := ctx.Param("id")
	namespace := ctx.Param("namespace")

	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 创建ConfigMap
	result, err := k8sClient.CreateConfigMap(namespace, requestData)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("创建ConfigMap失败: %v", err),
			"status":  "failed",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   result,
		"status": "success",
	})
}

// GetKubernetesEvents 获取事件
// @Tags Kubernetes
// @Summary 获取事件
// @Description 获取指定Kubernetes集群的事件
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param limit query int false "限制数量" default(100)
// @Param namespace query string false "命名空间"
// @Param field_selector query string false "字段选择器"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/events [get]
func (c *KubernetesController) GetKubernetesEvents(ctx *gin.Context) {
	id := ctx.Param("id")
	limitStr := ctx.DefaultQuery("limit", "100")
	limit, _ := strconv.Atoi(limitStr)
	namespace := ctx.Query("namespace")
	fieldSelector := ctx.Query("field_selector")

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 获取事件
	result, err := k8sClient.GetEvents(namespace, fieldSelector, limit)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("获取事件失败: %v", err),
			"status":  "failed",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":   20000,
		"data":   result,
		"status": "success",
	})
}

// BatchDeleteKubernetesPods 批量删除Pod
// @Tags Kubernetes
// @Summary 批量删除Pod
// @Description 批量删除指定Kubernetes集群中的Pod
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param namespace path string true "命名空间"
// @Param data body map[string]interface{} true "要删除的Pod名称列表"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/namespaces/{namespace}/pods/batch [delete]
func (c *KubernetesController) BatchDeleteKubernetesPods(ctx *gin.Context) {
	id := ctx.Param("id")
	namespace := ctx.Param("namespace")

	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	podNames, ok := requestData["pod_names"].([]interface{})
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "缺少pod_names参数",
		})
		return
	}

	// 转换为字符串数组
	podNameStrings := make([]string, len(podNames))
	for i, name := range podNames {
		if str, ok := name.(string); ok {
			podNameStrings[i] = str
		} else {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": fmt.Sprintf("Pod名称 %d 不是字符串类型", i),
			})
			return
		}
	}

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 批量删除Pod
	result, err := k8sClient.DeletePods(namespace, podNameStrings)

	// 记录审计日志
	status := models.AuditStatusSuccess
	errorMsg := ""
	if err != nil {
		status = models.AuditStatusFailed
		errorMsg = err.Error()
	}

	c.auditSvc.LogKubernetesOperation(ctx, cluster.ID, cluster.Name,
		models.AuditActionBatch, models.AuditResourcePod, "", namespace,
		fmt.Sprintf("批量删除Pod，共%d个", len(podNameStrings)),
		map[string]interface{}{
			"pod_names": podNameStrings,
			"result":    result,
		},
		status, errorMsg)

	// 发送WebSocket通知
	c.wsSvc.BroadcastToCluster(id, "pods_batch_deleted", map[string]interface{}{
		"pod_names": podNameStrings,
		"namespace": namespace,
		"cluster":   cluster.Name,
		"result":    result,
	})

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("批量删除Pod失败: %v", err),
			"status":  "failed",
			"data":    result,
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "批量删除Pod成功",
		"status":  "success",
		"data":    result,
	})
}

// BatchCreateKubernetesResources 批量创建资源
// @Tags Kubernetes
// @Summary 批量创建资源
// @Description 批量创建指定类型的Kubernetes资源
// @Accept json
// @Produce json
// @Param id path int true "集群ID"
// @Param type path string true "资源类型(namespace/service/configmap)"
// @Param data body map[string]interface{} true "要创建的资源列表"
// @Success 200 {object} map[string]interface{} "成功响应"
// @Failure 400 {object} map[string]interface{} "请求错误"
// @Failure 500 {object} map[string]interface{} "服务器错误"
// @Router /api/v1/cmdb/kubernetes/{id}/resources/{type}/batch [post]
func (c *KubernetesController) BatchCreateKubernetesResources(ctx *gin.Context) {
	id := ctx.Param("id")
	resourceType := ctx.Param("type")

	var requestData map[string]interface{}
	if err := ctx.ShouldBindJSON(&requestData); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	resources, ok := requestData["resources"].([]interface{})
	if !ok {
		ctx.JSON(http.StatusBadRequest, gin.H{
			"code":    40000,
			"message": "缺少resources参数",
		})
		return
	}

	// 转换为map数组
	resourceMaps := make([]map[string]interface{}, len(resources))
	for i, resource := range resources {
		if resMap, ok := resource.(map[string]interface{}); ok {
			resourceMaps[i] = resMap
		} else {
			ctx.JSON(http.StatusBadRequest, gin.H{
				"code":    40000,
				"message": fmt.Sprintf("资源 %d 格式错误", i),
			})
			return
		}
	}

	var cluster models.KubernetesCluster
	if err := c.db.First(&cluster, id).Error; err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{
			"code":    40404,
			"message": "K8s集群不存在",
		})
		return
	}

	k8sClient, err := utils.NewK8sClient(c.cfg.Encrypt.SecretKey, c.cfg.Encrypt.Salt, c.cfg.Encrypt.Info, &cluster)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("连接Kubernetes集群失败: %v", err),
			"status":  "failed",
		})
		return
	}

	// 批量创建资源
	result, err := k8sClient.BatchCreateResources(resourceType, resourceMaps)

	// 记录审计日志
	status := models.AuditStatusSuccess
	errorMsg := ""
	if err != nil {
		status = models.AuditStatusFailed
		errorMsg = err.Error()
	}

	c.auditSvc.LogKubernetesOperation(ctx, cluster.ID, cluster.Name,
		models.AuditActionBatch, resourceType, "", "",
		fmt.Sprintf("批量创建%s，共%d个", resourceType, len(resourceMaps)),
		map[string]interface{}{
			"resource_type": resourceType,
			"resources":     resourceMaps,
			"result":        result,
		},
		status, errorMsg)

	// 发送WebSocket通知
	c.wsSvc.BroadcastToCluster(id, "resources_batch_created", map[string]interface{}{
		"resource_type": resourceType,
		"resources":     resourceMaps,
		"cluster":       cluster.Name,
		"result":        result,
	})

	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"code":    50000,
			"message": fmt.Sprintf("批量创建资源失败: %v", err),
			"status":  "failed",
			"data":    result,
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"code":    20000,
		"message": "批量创建资源成功",
		"status":  "success",
		"data":    result,
	})
}
