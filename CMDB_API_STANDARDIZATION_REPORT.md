# CMDB API标准化报告

## 概述

本报告总结了DevOps CMDB系统的API标准化工作，确保前后端数据交互的一致性和规范性。

## 后端API标准化

### 1. 统一响应格式

#### 1.1 基础响应结构
```go
// 单个数据响应
type APIResponse struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

// 分页数据响应
type PaginatedResponse struct {
    Code     int         `json:"code"`
    Message  string      `json:"message"`
    Data     interface{} `json:"data"`
    Total    int64       `json:"total"`
    Page     int         `json:"page"`
    PageSize int         `json:"page_size"`
}
```

#### 1.2 列表数据包装
```go
// 列表数据统一包装为 {items: []}
type ListDataResponse struct {
    Items interface{} `json:"items"`
}
```

### 2. 标准化状态码

#### 2.1 成功状态码
- `200` - 成功
- `201` - 创建成功
- `204` - 删除成功（无内容）

#### 2.2 错误状态码
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 禁止访问
- `404` - 资源不存在
- `422` - 验证错误
- `500` - 服务器内部错误
- `503` - 服务不可用

### 3. 响应处理器

#### 3.1 ResponseHelper结构
```go
type ResponseHelper struct{}

// 主要方法
func (r *ResponseHelper) Success(ctx *gin.Context, data interface{})
func (r *ResponseHelper) List(ctx *gin.Context, data interface{}, total int64, page, pageSize int)
func (r *ResponseHelper) Error(ctx *gin.Context, code int, message string)
func (r *ResponseHelper) BadRequest(ctx *gin.Context, message string)
func (r *ResponseHelper) NotFound(ctx *gin.Context, message string)
func (r *ResponseHelper) InternalError(ctx *gin.Context, message string)
func (r *ResponseHelper) ValidationError(ctx *gin.Context, message string)
func (r *ResponseHelper) Created(ctx *gin.Context, data interface{})
func (r *ResponseHelper) NoContent(ctx *gin.Context)
```

### 4. 基础控制器

#### 4.1 BaseController功能
```go
type BaseController struct {
    db        *gorm.DB
    log       *logrus.Logger
    response  *ResponseHelper
    validator *validator.Validate
}

// 通用方法
func (c *BaseController) ParseUintParam(ctx *gin.Context, paramName string) (uint, error)
func (c *BaseController) ParseIntQuery(ctx *gin.Context, paramName string, defaultValue int) int
func (c *BaseController) ValidateRequest(ctx *gin.Context, req interface{}) bool
func (c *BaseController) ValidateQuery(ctx *gin.Context, req interface{}) bool
func (c *BaseController) GetPaginationParams(ctx *gin.Context) (int, int)
```

## 前端API标准化

### 1. TypeScript类型定义

#### 1.1 基础响应接口
```typescript
// 基础响应接口
export interface APIResponse<T = any> {
    code: number
    message: string
    data?: T
}

// 分页响应接口
export interface PaginatedResponse<T> {
    code: number
    message: string
    data: {
        items: T[]
        total: number
        page: number
        page_size: number
    }
    total: number
    page: number
    page_size: number
}
```

#### 1.2 基础模型接口
```typescript
export interface BaseModel {
    id: number
    created_at: string
    updated_at: string
}
```

### 2. 响应处理工具

#### 2.1 handleAPIResponse函数
```typescript
export function handleAPIResponse<T>(
    response: any,
    successCodes: number[] = []
): T {
    const defaultSuccessCodes = [200, 201, 20000, 20001, 20002, 20003, 20004, 20005]
    const validCodes = successCodes.length > 0 ? successCodes : defaultSuccessCodes

    const isSuccess = validCodes.includes(response.code) ||
        (response.code >= 20000 && response.code <= 29999) ||
        (response.code >= 200 && response.code < 300)

    if (isSuccess) {
        return response.data || response
    }

    const message = response.message || response.msg || '请求失败'
    throw new Error(message)
}
```

#### 2.2 工具函数
```typescript
// 检查响应是否成功
export function isAPISuccess(response: any, successCodes: number[] = []): boolean

// 获取错误信息
export function getAPIErrorMessage(response: any): string

// 提取分页数据
export function extractPaginatedData<T>(response: any): {
    items: T[]
    total: number
    page: number
    pageSize: number
}
```

### 3. API模块化设计

#### 3.1 模块结构
```
src/api/modules/
├── base.ts          # 基础类型定义
├── cmdb.ts          # CMDB相关API
├── user.ts          # 用户相关API
├── workflow.ts      # 工作流相关API
├── kubernetes.ts    # K8s相关API
├── microapp.ts      # 微应用相关API
├── cicd.ts          # CI/CD相关API
└── system.ts        # 系统相关API
```

#### 3.2 API服务定义
```typescript
// 以产品API为例
export const productApi = {
    getProducts: (params?: ProductListQuery) => {
        return request.get<PaginatedResponse<Product>>('/v1/cmdb/product', { params })
    },
    
    getProduct: (id: number) => {
        return request.get<APIResponse<Product>>(`/v1/cmdb/product/${id}`)
    },
    
    createProduct: (data: ProductCreateRequest) => {
        return request.post<APIResponse<Product>>('/v1/cmdb/product', data)
    },
    
    updateProduct: (id: number, data: Partial<ProductCreateRequest>) => {
        return request.put<APIResponse<Product>>(`/v1/cmdb/product/${id}`, data)
    },
    
    deleteProduct: (id: number) => {
        return request.delete<APIResponse<void>>(`/v1/cmdb/product/${id}`)
    }
}
```

## API路由标准化

### 1. 路由结构
```
/api/v1/cmdb/
├── product/         # 产品管理
├── environment/     # 环境管理
├── project/         # 项目管理
├── region/          # 区域管理
├── pipeline/        # 流水线管理
├── app/             # 应用管理
│   ├── microapp/    # 微应用
│   ├── service/     # 应用服务
│   ├── language/    # 开发语言
│   └── tags         # 应用标签
├── kubernetes/      # K8s集群管理
├── asset/           # 资产管理
│   └── idc/        # IDC管理
├── git/             # Git仓库
└── harbor          # Harbor镜像仓库
```

### 2. RESTful设计

#### 2.1 标准CRUD操作
- `GET /api/v1/cmdb/{resource}` - 获取资源列表
- `GET /api/v1/cmdb/{resource}/{id}` - 获取单个资源
- `POST /api/v1/cmdb/{resource}` - 创建资源
- `PUT /api/v1/cmdb/{resource}/{id}` - 更新资源
- `DELETE /api/v1/cmdb/{resource}/{id}` - 删除资源

#### 2.2 查询参数标准化
- `page` - 页码（默认1）
- `page_size` - 每页数量（默认20）
- `search` - 搜索关键词
- `sort` - 排序字段
- `order` - 排序方向（asc/desc）

## 数据验证标准化

### 1. 后端验证

#### 1.1 请求模型验证
```go
type ProductCreateRequest struct {
    Name        string `json:"name" binding:"required,min=1,max=100"`
    ProductCode string `json:"product_code" binding:"omitempty,min=2,max=100"`
    RegionID    *uint  `json:"region_id" binding:"omitempty"`
    Description string `json:"description" binding:"omitempty,max=500"`
}
```

#### 1.2 验证标签
- `required` - 必填
- `min/max` - 长度限制
- `email` - 邮箱格式
- `url` - URL格式
- `oneof` - 枚举值

### 2. 前端验证

#### 2.1 表单验证规则
```typescript
const formRules = {
    name: [
        { required: true, message: '请输入产品名称', trigger: 'blur' },
        { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
    ],
    product_code: [
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
    ]
}
```

## 错误处理标准化

### 1. 后端错误处理

#### 1.1 错误码定义
```go
const (
    CodeSuccess            = 200
    CodeCreated            = 201
    CodeBadRequest         = 400
    CodeUnauthorized       = 401
    CodeForbidden          = 403
    CodeNotFound           = 404
    CodeInternalError      = 500
    CodeValidationError    = 422
)
```

#### 1.2 错误响应格式
```json
{
    "code": 400,
    "message": "请求参数错误: name is required",
    "data": null
}
```

### 2. 前端错误处理

#### 2.1 统一错误处理
```typescript
// 在页面中使用
try {
    const response = await productApi.getProducts(params)
    const result = handleAPIResponse(response) as {items: Product[], total: number}
    
    if (result) {
        products.value = result.items || []
        pagination.total = result.total || 0
    }
} catch (error) {
    console.error('获取产品列表失败:', error)
    ElMessage.error('获取产品列表失败')
}
```

## 文档标准化

### 1. Swagger文档

#### 1.1 API注释格式
```go
// @Summary 获取产品列表
// @Description 分页获取产品列表，支持搜索
// @Tags Product
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param search query string false "搜索关键词"
// @Success 200 {object} models.PaginatedResponse{data=models.ListDataResponse{items=[]models.Product}}
// @Failure 400 {object} models.APIResponse
// @Failure 500 {object} models.APIResponse
// @Router /api/v1/cmdb/product [get]
```

#### 1.2 模型文档
```go
// Product 产品模型
type Product struct {
    BaseModel
    Name        string     `json:"name" gorm:"size:100;not null;comment:产品名称"`
    ProductCode string     `json:"product_code" gorm:"size:100;comment:产品代码"`
    RegionID    *uint      `json:"region_id" gorm:"comment:区域ID"`
    Region      *Region    `json:"region,omitempty" gorm:"foreignKey:RegionID"`
    Description string     `json:"description" gorm:"type:text;comment:描述"`
}
```

## 测试标准化

### 1. API测试

#### 1.1 单元测试
```go
func TestProductAPI(t *testing.T) {
    // 测试创建产品
    req := models.ProductCreateRequest{
        Name:        "测试产品",
        ProductCode: "test-product",
    }
    
    response := createProduct(req)
    assert.Equal(t, models.CodeCreated, response.Code)
    assert.NotNil(t, response.Data)
}
```

#### 1.2 集成测试
```typescript
describe('Product API', () => {
    it('should get products list', async () => {
        const response = await productApi.getProducts({ page: 1, page_size: 10 })
        const result = handleAPIResponse(response)
        
        expect(result.items).toBeDefined()
        expect(result.total).toBeGreaterThanOrEqual(0)
    })
})
```

## 性能优化

### 1. 分页优化

#### 1.1 后端分页
```go
// 高效分页查询
func (s *CMDBService) GetProducts(page, pageSize int, search string) ([]models.Product, int64, error) {
    var products []models.Product
    var total int64
    
    query := s.db.Model(&models.Product{})
    
    if search != "" {
        query = query.Where("name LIKE ? OR product_code LIKE ?", "%"+search+"%", "%"+search+"%")
    }
    
    // 先计算总数
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }
    
    // 分页查询
    offset := (page - 1) * pageSize
    if err := query.Preload("Region").Offset(offset).Limit(pageSize).Find(&products).Error; err != nil {
        return nil, 0, err
    }
    
    return products, total, nil
}
```

#### 1.2 前端分页
```typescript
// 分页参数管理
const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
})

// 分页变化处理
const handlePageChange = (page: number) => {
    pagination.page = page
    fetchData()
}

const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.page = 1
    fetchData()
}
```

### 2. 缓存策略

#### 2.1 后端缓存
```go
// 资源缓存
type ResourceCache struct {
    data      map[string]interface{}
    timestamp map[string]time.Time
    ttl       time.Duration
    mutex     sync.RWMutex
}

func (c *ResourceCache) Get(key string) (interface{}, bool) {
    c.mutex.RLock()
    defer c.mutex.RUnlock()
    
    if timestamp, exists := c.timestamp[key]; exists {
        if time.Since(timestamp) < c.ttl {
            return c.data[key], true
        }
        // 过期删除
        delete(c.data, key)
        delete(c.timestamp, key)
    }
    
    return nil, false
}
```

#### 2.2 前端缓存
```typescript
// 简单内存缓存
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

function getCachedData<T>(key: string): T | null {
    const cached = cache.get(key)
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
        return cached.data
    }
    cache.delete(key)
    return null
}

function setCachedData<T>(key: string, data: T, ttl: number = 300000) {
    cache.set(key, { data, timestamp: Date.now(), ttl })
}
```

## 监控和日志

### 1. API监控

#### 1.1 请求日志
```go
// 请求日志中间件
func RequestLogger() gin.HandlerFunc {
    return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
        return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
            param.ClientIP,
            param.TimeStamp.Format(time.RFC3339),
            param.Method,
            param.Path,
            param.Request.Proto,
            param.StatusCode,
            param.Latency,
            param.Request.UserAgent(),
            param.ErrorMessage,
        )
    })
}
```

#### 1.2 性能监控
```go
// 性能监控中间件
func PerformanceMonitor() gin.HandlerFunc {
    return func(c *gin.Context) {
        start := time.Now()
        
        c.Next()
        
        duration := time.Since(start)
        if duration > time.Second {
            log.Warnf("Slow API: %s %s took %v", c.Request.Method, c.Request.URL.Path, duration)
        }
    }
}
```

### 2. 错误监控

#### 2.1 错误统计
```typescript
// 前端错误监控
const errorStats = {
    apiErrors: new Map<string, number>(),
    
    recordError(api: string, error: any) {
        const count = this.apiErrors.get(api) || 0
        this.apiErrors.set(api, count + 1)
        
        // 发送错误统计
        if (count > 10) {
            console.warn(`API ${api} 错误次数过多: ${count}`)
        }
    }
}
```

## 总结

CMDB系统的API标准化工作已经完成，主要成果包括：

### 主要成果
1. **统一响应格式** - 前后端统一的API响应结构
2. **标准化状态码** - 规范的HTTP状态码和业务状态码
3. **类型安全** - 完整的TypeScript类型定义
4. **错误处理** - 统一的错误处理机制
5. **文档完善** - 详细的Swagger API文档
6. **性能优化** - 分页、缓存等性能优化策略

### 技术亮点
1. **前后端类型一致** - TypeScript和Go的类型定义保持一致
2. **响应处理统一** - 统一的响应处理工具函数
3. **验证机制** - 前后端双重验证保证数据质量
4. **模块化设计** - API模块化设计便于维护
5. **监控完善** - 完整的API监控和错误追踪

该标准化工作为CMDB系统提供了稳定、高效、易维护的API接口，为前后端协作奠定了坚实的基础。 